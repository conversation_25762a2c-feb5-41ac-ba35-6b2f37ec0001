{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/apis/auth/auth-apis.ts", "./src/apis/talent/documents.ts", "./src/apis/talent/emergency-contact.ts", "./src/apis/talent/profile.ts", "./src/apis/talent/security.ts", "./src/apis/talent/skills.ts", "./src/composables/auth/uselogin.ts", "./src/composables/common/usepagination.ts", "./src/composables/talent/usedocuments.ts", "./src/composables/talent/usephoneinput.ts", "./src/composables/talent/useskillsmanager.ts", "./src/composables/talent/usetalentprofile.ts", "./src/composables/talent/usetalenttable.ts", "./src/router/index.ts", "./src/store/auth/useauthstore.ts", "./src/store/employee/usetalentstore.ts", "./src/store/talent/usetalentprofilestore.ts", "./src/types/documents.ts", "./src/types/talent.ts", "./src/utils/case-converter.ts", "./src/utils/date-util.ts", "./src/utils/http-handler.ts", "./src/utils/image-background.ts", "./src/app.vue", "./src/components/calendar.vue", "./src/components/helloworld.vue", "./src/components/sidebar.vue", "./src/components/statusmodal.vue", "./src/components/talenttable.vue", "./src/components/tabs/bankinginfo.vue", "./src/components/tabs/documents.vue", "./src/components/tabs/health.vue", "./src/components/tabs/historylog.vue", "./src/components/tabs/jobrelatedinfo.vue", "./src/components/tabs/talentprofile.vue", "./src/components/tabs/workhistory.vue", "./src/views/historylogview.vue", "./src/views/loginpage.vue", "./src/views/notfoundview.vue", "./src/views/placeholderview.vue", "./src/views/settingsview.vue", "./src/views/roles/addroleview.vue", "./src/views/roles/roleslistview.vue", "./src/views/roles/toggleswitch.vue", "./src/views/talent/addtalentpage.vue", "./src/views/talent/talentlistview.vue", "./src/views/users/adduserview.vue", "./src/views/users/userslistview.vue"], "errors": true, "version": "5.8.3"}