export interface BpoRelatedInfoCreate {
  id?: number
  talentProfileId: number
  reportingManager: string
  reportingDepartment: string
  campaign: string
  bpoEmail: string
  location?: string
  isLocationPermanent?: boolean
  locationDuration?: string
  position: string
  role: string
  title: string
  shiftStartTime?: string
  shiftEndTime?: string
  isContract: string
  contractEndDate?: string
  terminationDate?: string
  reasonForTermination?: string
  startDate: string
  hoursPerWeek: string
  hourlyRate: string
  workingWeekDays: string[]
  wageBase: string
  wageCurrency: string
  wageFrequency: string
  workDays: string[]
  notes?: string
}

export interface VacationsInfo {
  id?: number
  yearNumber: number
  talentProfileId: number
  usedVacationDays: number
  availableVacationDays: number
  remainingVacationDays: number
}
