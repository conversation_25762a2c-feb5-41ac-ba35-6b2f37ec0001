<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";
import { X, Calendar, ChevronDown } from "lucide-vue-next";
import CalendarComponent from "@/components/Calendar.vue";
import { datePickerFormatter } from "@/utils/date-util";
import { useBpoRelated } from "@/composables/talent/useBpoRelated";

const {
  // State
  labelStates,
  vacationForm,
  bpoRelatedInfoForm,
  IsEditMode,

  // Date picker states
  showBpoStartDatePicker,
  showTerminationDatePicker,
  showContractEndDatePicker,

  // Dropdown states
  dropdownStates,

  // Work days
  workDaysOptions,

  // Vacation calculations
  yearsOfService,
  totalVacationDays,
  remainingVacationDays,

  // Methods
  setLabelActive,
  getFilteredOptions,

  // BPO date picker methods
  toggleBpoStartDatePicker,
  updateBpoStartDate,
  clearBpoStartDate,
  toggleTerminationDatePicker,
  updateTerminationDate,
  clearTerminationDate,
  toggleContractEndDatePicker,
  updateContractEndDate,
  clearContractEndDate,

  // Dropdown methods
  selectBpoOption,
  handleDropdownInput,
  handleDropdownBlur,

  // Work day methods
  removeWorkDay,
  addPredefinedWorkDay,

  // API methods
  getTalentProfileBpoRelatedInfo,
  submitForm,
  submitVacations,
  getTalentProfileVacationsInfo,

  // Utility methods
  preventAtSymbol,
} = useBpoRelated();

// Click outside handler to close calendars
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;

  // Check if click is outside any calendar component or date input
  const isCalendarClick =
    target.closest(".calendar-component") ||
    target.closest("[data-calendar-trigger]") ||
    target.closest(".floating-label-container");

  if (!isCalendarClick) {
    // Close all calendars
    showBpoStartDatePicker.value = false;
    showTerminationDatePicker.value = false;
    showContractEndDatePicker.value = false;
  }
};

// Add event listener on mount
onMounted(() => {
  if (IsEditMode.value) {
    getTalentProfileBpoRelatedInfo();
    getTalentProfileVacationsInfo();
  }
  document.addEventListener("click", handleClickOutside);
});

// Remove event listener on unmount
onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

const submitJobRelatedForm = () => {
  submitForm();
  submitVacations();
};
</script>

<template>
  <div class="px-8 space-y-6">
    <!-- Organization & Management Section -->
    <div class="material-section">
      <h3 class="mb-4 text-lg font-medium text-gray-900">
        Organization & Management
      </h3>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <!-- Reporting Department -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="bpoRelatedInfoForm.reportingDepartment"
                type="text"
                class="pr-8 floating-input"
                @focus="
                  setLabelActive('reportingDepartment', true);
                  handleDropdownInput('reportingDepartment');
                "
                @blur="handleDropdownBlur('reportingDepartment')"
                @input="handleDropdownInput('reportingDepartment')"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active:
                      labelStates.reportingDepartment ||
                      bpoRelatedInfoForm.reportingDepartment,
                  },
                ]"
              >
                Reporting Department
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.reportingDepartment &&
                  getFilteredOptions('reportingDepartment').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('reportingDepartment')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectBpoOption('reportingDepartment', option)"
                >
                  {{ option }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- BPO Manager -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="bpoRelatedInfoForm.reportingManager"
                type="text"
                class="pr-8 floating-input"
                @focus="
                  setLabelActive('bpoManager', true);
                  handleDropdownInput('bpoManager');
                "
                @blur="handleDropdownBlur('bpoManager')"
                @input="handleDropdownInput('bpoManager')"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active:
                      labelStates.bpoManager ||
                      bpoRelatedInfoForm.reportingManager,
                  },
                ]"
              >
                BPO Manager
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.bpoManager &&
                  getFilteredOptions('bpoManager').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('bpoManager')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectBpoOption('reportingManager', option)"
                >
                  {{ option }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Campaign -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="bpoRelatedInfoForm.campaign"
                type="text"
                class="pr-8 floating-input"
                @focus="
                  setLabelActive('campaign', true);
                  handleDropdownInput('campaign');
                "
                @blur="handleDropdownBlur('campaign')"
                @input="handleDropdownInput('campaign')"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active: labelStates.campaign || bpoRelatedInfoForm.campaign,
                  },
                ]"
              >
                Campaign
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.campaign &&
                  getFilteredOptions('campaign').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('campaign')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectBpoOption('campaign', option)"
                >
                  {{ option }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-4 mt-4 md:grid-cols-2">
        <!-- BPO Email -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="bpoRelatedInfoForm.bpoEmail"
                type="text"
                class="pr-48 floating-input"
                @focus="setLabelActive('bpoEmail', true)"
                @blur="setLabelActive('bpoEmail', false)"
                @keydown="preventAtSymbol"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active: labelStates.bpoEmail || bpoRelatedInfoForm.bpoEmail,
                  },
                ]"
              >
                BPO Email
              </label>
              <!-- Email domain suffix -->
              <span
                class="absolute right-3 top-1/2 text-sm text-gray-500 transform -translate-y-1/2 pointer-events-none"
              >
                @bposolutionsgroup.com
              </span>
            </div>
          </div>
        </div>

        <!-- Location -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="bpoRelatedInfoForm.location"
                type="text"
                class="pr-8 floating-input"
                @focus="
                  setLabelActive('location', true);
                  handleDropdownInput('location');
                "
                @blur="handleDropdownBlur('location')"
                @input="handleDropdownInput('location')"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active: labelStates.location || bpoRelatedInfoForm.location,
                  },
                ]"
              >
                Location (Optional)
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.location &&
                  getFilteredOptions('location').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('location')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectBpoOption('location', option)"
                >
                  {{ option }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- New row for permanent status and duration -->
      <div class="grid grid-cols-1 gap-4 mt-4 md:grid-cols-2">
        <!-- Is Permanent -->
        <div>
          <h4 class="mb-3 text-sm font-medium text-gray-700">Is Permanent (Optional)</h4>
          <div class="flex items-center space-x-6">
            <label class="flex items-center">
              <input
                v-model="bpoRelatedInfoForm.isLocationPermanent"
                type="radio"
                :value=true
                class="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
              />
              <span class="ml-2 text-sm text-gray-700">Yes</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="bpoRelatedInfoForm.isLocationPermanent"
                type="radio"
                :value=false
                class="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
              />
              <span class="ml-2 text-sm text-gray-700">No</span>
            </label>
          </div>
        </div>

        <!-- Duration in Months (only show if "No" is selected) -->
        <div v-if="!bpoRelatedInfoForm.isLocationPermanent">
          <div class="floating-label-container">
            <input
              v-model="bpoRelatedInfoForm.locationDuration"
              type="number"
              min="1"
              class="floating-input"
              @focus="setLabelActive('durationInMonths', true)"
              @blur="setLabelActive('durationInMonths', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active: labelStates.durationInMonths || bpoRelatedInfoForm.locationDuration,
                },
              ]"
            >
              Duration in Months
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Position & Role Information Section -->
    <div class="material-section">
      <h3 class="mb-4 text-lg font-medium text-gray-900">
        Position & Role Information
      </h3>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <!-- Position -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bpoRelatedInfoForm.position"
              type="text"
              class="floating-input"
              @focus="setLabelActive('position', true)"
              @blur="setLabelActive('position', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active: labelStates.position || bpoRelatedInfoForm.position,
                },
              ]"
            >
              Position
            </label>
          </div>
        </div>

        <!-- BPO Role -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="bpoRelatedInfoForm.role"
                type="text"
                class="pr-8 floating-input"
                @focus="
                  setLabelActive('bpoRole', true);
                  handleDropdownInput('bpoRole');
                "
                @blur="handleDropdownBlur('bpoRole')"
                @input="handleDropdownInput('bpoRole')"
              />
              <label
                :class="[
                  'floating-label',
                  { active: labelStates.bpoRole || bpoRelatedInfoForm.role },
                ]"
              >
                BPO Role
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.bpoRole &&
                  getFilteredOptions('bpoRole').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('bpoRole')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectBpoOption('role', option)"
                >
                  {{ option }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- BPO Title -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bpoRelatedInfoForm.title"
              type="text"
              class="floating-input"
              @focus="setLabelActive('bpoTitle', true)"
              @blur="setLabelActive('bpoTitle', false)"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.bpoTitle || bpoRelatedInfoForm.title },
              ]"
            >
              BPO Title
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Schedule & Timing Section -->
    <div class="material-section">
      <h3 class="mb-4 text-lg font-medium text-gray-900">Schedule & Timing</h3>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <!-- Shift Start Time -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bpoRelatedInfoForm.shiftStartTime"
              type="time"
              class="floating-input"
              @focus="setLabelActive('shiftStartTime', true)"
              @blur="setLabelActive('shiftStartTime', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active:
                    labelStates.shiftStartTime ||
                    bpoRelatedInfoForm.shiftStartTime,
                },
              ]"
            >
              Shift Start Time
            </label>
          </div>
        </div>

        <!-- Shift End Time -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bpoRelatedInfoForm.shiftEndTime"
              type="time"
              class="floating-input"
              @focus="setLabelActive('shiftEndTime', true)"
              @blur="setLabelActive('shiftEndTime', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active:
                    labelStates.shiftEndTime || bpoRelatedInfoForm.shiftEndTime,
                },
              ]"
            >
              Shift End Time
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Employment Information Section -->
    <div class="material-section">
      <h3 class="mb-4 text-lg font-medium text-gray-900">
        Employment Information
      </h3>

      <!-- Employment Type -->
      <div class="mb-6">
        <h4 class="mb-3 text-sm font-medium text-gray-700">Employment Type</h4>
        <div class="flex items-center space-x-6">
          <label class="flex items-center">
            <input
              v-model="bpoRelatedInfoForm.isContract"
              type="radio"
              value="permanent"
              class="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
            />
            <span class="ml-2 text-sm text-gray-700">Permanent</span>
          </label>
          <label class="flex items-center">
            <input
              v-model="bpoRelatedInfoForm.isContract"
              type="radio"
              value="contract"
              class="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
            />
            <span class="ml-2 text-sm text-gray-700">Contract</span>
          </label>
        </div>
      </div>

      <!-- Employment Dates -->
      <div class="mb-6">
        <h4 class="mb-3 text-sm font-medium text-gray-700">Employment Dates</h4>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <!-- Start Date -->
          <div>
            <div class="floating-label-container">
              <div class="relative">
                <input
                  :value="
                    bpoRelatedInfoForm.startDate
                      ? datePickerFormatter(
                          new Date(bpoRelatedInfoForm.startDate)
                        )
                      : ''
                  "
                  type="text"
                  readonly
                  data-calendar-trigger
                  class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                  @click="toggleBpoStartDatePicker"
                  @focus="setLabelActive('startDate', true)"
                  @blur="setLabelActive('startDate', false)"
                />
                <label
                  :class="[
                    'floating-label',
                    {
                      active:
                        labelStates.startDate || bpoRelatedInfoForm.startDate,
                    },
                  ]"
                >
                  Start Date
                </label>
                <!-- Clear button -->
                <button
                  v-if="bpoRelatedInfoForm.startDate"
                  @click.stop="clearBpoStartDate"
                  class="absolute right-12 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
                >
                  <X :size="16" />
                </button>
                <!-- Calendar icon -->
                <Calendar
                  :size="16"
                  class="absolute right-4 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                />
                <CalendarComponent
                  :is-open="showBpoStartDatePicker"
                  :selected-date="
                    bpoRelatedInfoForm.startDate
                      ? new Date(bpoRelatedInfoForm.startDate)
                      : new Date()
                  "
                  @select-date="updateBpoStartDate"
                  @close="showBpoStartDatePicker = false"
                />
              </div>
            </div>
          </div>

          <!-- Termination Date -->
          <div v-if="bpoRelatedInfoForm.isContract !== 'contract'">
            <div class="floating-label-container">
              <div class="relative">
                <input
                  :value="
                    bpoRelatedInfoForm.terminationDate
                      ? datePickerFormatter(
                          new Date(bpoRelatedInfoForm.terminationDate)
                        )
                      : ''
                  "
                  type="text"
                  readonly
                  data-calendar-trigger
                  class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                  @click="toggleTerminationDatePicker"
                  @focus="setLabelActive('terminationDate', true)"
                  @blur="setLabelActive('terminationDate', false)"
                />
                <label
                  :class="[
                    'floating-label',
                    {
                      active:
                        labelStates.terminationDate ||
                        bpoRelatedInfoForm.terminationDate,
                    },
                  ]"
                >
                  Termination Date
                </label>
                <!-- Clear button -->
                <button
                  v-if="bpoRelatedInfoForm.terminationDate"
                  @click.stop="clearTerminationDate"
                  class="absolute right-12 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
                >
                  <X :size="16" />
                </button>
                <!-- Calendar icon -->
                <Calendar
                  :size="16"
                  class="absolute right-4 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                />
                <CalendarComponent
                  :is-open="showTerminationDatePicker"
                  :selected-date="
                    bpoRelatedInfoForm.terminationDate
                      ? new Date(bpoRelatedInfoForm.terminationDate)
                      : new Date()
                  "
                  @select-date="updateTerminationDate"
                  @close="showTerminationDatePicker = false"
                />
              </div>
            </div>
          </div>

          <!-- Contract End Date -->
          <div v-if="bpoRelatedInfoForm.isContract === 'contract'">
            <div class="floating-label-container">
              <div class="relative">
                <input
                  :value="
                    bpoRelatedInfoForm.contractEndDate
                      ? datePickerFormatter(
                          new Date(bpoRelatedInfoForm.contractEndDate)
                        )
                      : ''
                  "
                  type="text"
                  readonly
                  data-calendar-trigger
                  class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                  @click="toggleContractEndDatePicker"
                  @focus="setLabelActive('contractEndDate', true)"
                  @blur="setLabelActive('contractEndDate', false)"
                />
                <label
                  :class="[
                    'floating-label',
                    {
                      active:
                        labelStates.contractEndDate ||
                        bpoRelatedInfoForm.contractEndDate,
                    },
                  ]"
                >
                  Contract End Date
                </label>
                <!-- Clear button -->
                <button
                  v-if="bpoRelatedInfoForm.contractEndDate"
                  @click.stop="clearContractEndDate"
                  class="absolute right-12 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
                >
                  <X :size="16" />
                </button>
                <!-- Calendar icon -->
                <Calendar
                  :size="16"
                  class="absolute right-4 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                />
                <CalendarComponent
                  :is-open="showContractEndDatePicker"
                  :selected-date="
                    bpoRelatedInfoForm.contractEndDate
                      ? new Date(bpoRelatedInfoForm.contractEndDate)
                      : new Date()
                  "
                  @select-date="updateContractEndDate"
                  @close="showContractEndDatePicker = false"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Termination Notes -->
        <div v-if="bpoRelatedInfoForm.isContract !== 'contract'" class="mt-4">
          <h4 class="mb-3 text-sm font-medium text-gray-700">
            Termination Notes
          </h4>
          <div class="floating-label-container">
            <textarea
              v-model="bpoRelatedInfoForm.reasonForTermination"
              rows="3"
              :disabled="!bpoRelatedInfoForm.terminationDate"
              :class="[
                'floating-textarea',
                {
                  'bg-gray-50 cursor-not-allowed text-gray-400': !bpoRelatedInfoForm.terminationDate,
                  'bg-white': bpoRelatedInfoForm.terminationDate
                }
              ]"
              @focus="bpoRelatedInfoForm.terminationDate && setLabelActive('reasonForTermination', true)"
              @blur="setLabelActive('reasonForTermination', false)"
            ></textarea>
            <label
              :class="[
                'floating-label',
                {
                  active:
                    labelStates.reasonForTermination ||
                    bpoRelatedInfoForm.reasonForTermination,
                  'text-gray-400': !bpoRelatedInfoForm.terminationDate,
                  'text-gray-700': bpoRelatedInfoForm.terminationDate
                },
              ]"
            >
              Notes for Termination
            </label>
          </div>
          <div v-if="!bpoRelatedInfoForm.terminationDate" class="mt-1 text-xs text-gray-500">
            Select a termination date to enable this field
          </div>
        </div>
      </div>
    </div>

    <!-- Compensation & Work Details Section -->
    <div class="material-section">
      <h3 class="mb-4 text-lg font-medium text-gray-900">
        Compensation & Work Details
      </h3>

      <div class="grid grid-cols-1 gap-4 mt-3 md:grid-cols-2">
        <!-- Hours Per Week -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bpoRelatedInfoForm.hoursPerWeek"
              type="number"
              min="1"
              max="168"
              class="floating-input"
              @focus="setLabelActive('hoursPerWeek', true)"
              @blur="setLabelActive('hoursPerWeek', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active:
                    labelStates.hoursPerWeek || bpoRelatedInfoForm.hoursPerWeek,
                },
              ]"
            >
              Hours Per Week
            </label>
          </div>
        </div>

        <!-- Hourly Rate -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bpoRelatedInfoForm.hourlyRate"
              type="number"
              step="0.01"
              min="0"
              class="floating-input"
              @focus="setLabelActive('hourlyRate', true)"
              @blur="setLabelActive('hourlyRate', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active:
                    labelStates.hourlyRate || bpoRelatedInfoForm.hourlyRate,
                },
              ]"
            >
              Hourly Rate
            </label>
          </div>
        </div>
      </div>

      <!-- Additional Wage Information -->
      <div class="grid grid-cols-1 gap-4 mt-4 md:grid-cols-3">
        <!-- Wage Base -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="bpoRelatedInfoForm.wageBase"
              type="number"
              step="0.01"
              min="0"
              class="floating-input"
              @focus="setLabelActive('wageBase', true)"
              @blur="setLabelActive('wageBase', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active: labelStates.wageBase || bpoRelatedInfoForm.wageBase,
                },
              ]"
            >
              Wage Base
            </label>
          </div>
        </div>

        <!-- Wage Currency -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="bpoRelatedInfoForm.wageCurrency"
                type="text"
                class="pr-8 floating-input"
                @focus="
                  setLabelActive('wageCurrency', true);
                  handleDropdownInput('wageCurrency');
                "
                @blur="handleDropdownBlur('wageCurrency')"
                @input="handleDropdownInput('wageCurrency')"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active:
                      labelStates.wageCurrency ||
                      bpoRelatedInfoForm.wageCurrency,
                  },
                ]"
              >
                Currency
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.wageCurrency &&
                  getFilteredOptions('wageCurrency').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('wageCurrency')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectBpoOption('wageCurrency', option)"
                >
                  {{ option }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Wage Frequency -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="bpoRelatedInfoForm.wageFrequency"
                type="text"
                class="pr-8 floating-input"
                @focus="
                  setLabelActive('wageFrequency', true);
                  handleDropdownInput('wageFrequency');
                "
                @blur="handleDropdownBlur('wageFrequency')"
                @input="handleDropdownInput('wageFrequency')"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active:
                      labelStates.wageFrequency ||
                      bpoRelatedInfoForm.wageFrequency,
                  },
                ]"
              >
                Frequency
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.wageFrequency &&
                  getFilteredOptions('wageFrequency').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('wageFrequency')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectBpoOption('wageFrequency', option)"
                >
                  {{ option }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Work Days Selection -->
      <div class="mt-4">
        <h4 class="mb-3 text-sm font-medium text-gray-700">Work Days</h4>

        <!-- Work Days Toggle Buttons/Tags -->
        <div class="flex flex-wrap gap-2">
          <div v-for="day in workDaysOptions" :key="day">
            <!-- Selected Day (Tag) -->
            <div
              v-if="bpoRelatedInfoForm.workingWeekDays.includes(day)"
              class="flex items-center px-3 py-1 text-sm bg-gray-50 rounded-full border"
            >
              <span class="text-gray-700">{{ day }}</span>
              <button
                @click="
                  removeWorkDay(bpoRelatedInfoForm.workingWeekDays.indexOf(day))
                "
                class="ml-2 text-red-600 transition-colors hover:text-red-700"
              >
                <X :size="14" />
              </button>
            </div>

            <!-- Unselected Day (Button) -->
            <button
              v-else
              @click="addPredefinedWorkDay(day)"
              class="px-3 py-1 text-sm rounded-full border border-gray-300 transition-colors hover:bg-gray-50 hover:border-orange-300"
            >
              {{ day }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Vacations Section -->
    <div class="material-section">
      <h3 class="mb-4 text-lg font-medium text-gray-900">Vacations</h3>

      <!-- Years of Service Info -->
      <div class="p-3 mb-4 bg-blue-50 rounded-lg border border-blue-200">
        <div class="flex justify-between items-center text-sm">
          <span class="font-medium text-blue-700">Years of Service:</span>
          <span class="font-semibold text-blue-900"
            >{{ yearsOfService }} year{{
              yearsOfService !== 1 ? "s" : ""
            }}</span
          >
        </div>
        <div class="mt-1 text-xs text-blue-600">
          Based on start date:
          {{
            bpoRelatedInfoForm.startDate
              ? new Date(bpoRelatedInfoForm.startDate).toLocaleDateString()
              : "Not set"
          }}
        </div>
      </div>

      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <!-- Vacations (Paid Days off) - Read Only -->
        <div>
          <div class="floating-label-container">
            <input
              :value="totalVacationDays"
              type="number"
              readonly
              class="bg-gray-50 cursor-not-allowed floating-input"
              @focus="setLabelActive('paidDaysOff', true)"
              @blur="setLabelActive('paidDaysOff', false)"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.paidDaysOff || totalVacationDays >= 0 },
              ]"
            >
              Vacations (Paid Days off)
            </label>
          </div>
          <div class="mt-1 text-xs text-gray-500">
            Auto-calculated based on years of service
          </div>
        </div>

        <!-- Vacations used -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="vacationForm.vacationsUsed"
              type="number"
              min="0"
              :max="totalVacationDays"
              class="floating-input"
              @focus="setLabelActive('vacationsUsed', true)"
              @blur="setLabelActive('vacationsUsed', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active:
                    labelStates.vacationsUsed || vacationForm.vacationsUsed,
                },
              ]"
            >
              Vacations used
            </label>
          </div>
          <div class="mt-1 text-xs text-gray-500">
            Maximum: {{ totalVacationDays }} days
          </div>
        </div>

        <!-- Remaining Vacations - Read Only -->
        <div>
          <div class="floating-label-container">
            <input
              :value="remainingVacationDays"
              type="number"
              readonly
              class="bg-gray-50 cursor-not-allowed floating-input"
              @focus="setLabelActive('remainingVacations', true)"
              @blur="setLabelActive('remainingVacations', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active:
                    labelStates.remainingVacations ||
                    remainingVacationDays >= 0,
                },
              ]"
            >
              Remaining Vacations
            </label>
          </div>
          <div class="mt-1 text-xs text-gray-500">
            Auto-calculated: {{ totalVacationDays }} -
            {{ vacationForm.vacationsUsed || 0 }} = {{ remainingVacationDays }}
          </div>
        </div>
      </div>
    </div>

    <!-- Notes Section -->
    <!-- <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">Notes</h3>
      <div class="floating-label-container">
        <textarea
          v-model="bpoRelatedInfoForm.notes"
          rows="4"
          class="floating-textarea"
          @focus="setLabelActive('notes', true)"
          @blur="setLabelActive('notes', false)"
        ></textarea>
        <label
          :class="[
            'floating-label',
            { active: labelStates.notes || bpoRelatedInfoForm.notes },
          ]"
        >
          Notes
        </label>
      </div>
    </div> -->

    <!-- Submit Button -->
    <div class="flex justify-end pt-6">
      <button
        class="text-white bg-gray-600 material-button hover:bg-gray-700"
        @click="submitJobRelatedForm"
      >
        SUBMIT
      </button>
    </div>
  </div>
</template>
