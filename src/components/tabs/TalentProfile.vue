<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { X, Plus } from "lucide-vue-next";
import CalendarComponent from "@/components/Calendar.vue";

// Composables
import { useTalentProfile } from "@/composables/talent/useTalentProfile";
import { usePhoneInput } from "@/composables/talent/usePhoneInput";
import { useSkillsManager } from "@/composables/talent/useSkillsManager";

// Store
import { useTalentProfileStore } from "@/store/talent/useTalentProfileStore";
import { datePickerFormatter } from "@/utils/date-util";
import { useRoute } from "vue-router";

// Route setup
const route = useRoute();

// Store setup
const store = useTalentProfileStore();
const { form, labelStates, contactLabelStates, isLoading, error } = store;

// Composables setup
const {
  fieldErrors,
  validateField,
  submitForm,
  getTalentSecurity,
  getTalentEmergencyContacts,
  getTalentSkillSet
} = useTalentProfile();
const { formatPhoneDisplay, handlePhoneInput } = usePhoneInput();
const { newSkill, addSkill, removeSkill } = useSkillsManager();

// Date picker state
const showDatePicker = ref(false);

const isEditMode = computed(() => !!route.params.id);

const talentId = computed(() => route.params.id ? Number(route.params.id) : 0);

// Phone input handlers
const handlePrimaryPhoneInput = (event: Event) => {
  handlePhoneInput(event, (value) =>
    store.updateField("primaryContact", value)
  );
};

const handleSecondaryPhoneInput = (event: Event) => {
  handlePhoneInput(event, (value) =>
    store.updateField("secondaryContact", value)
  );
};

const handleEmergencyPhoneInput = (event: Event, index: number) => {
  handlePhoneInput(event, (value) => {
    form.emergencyContacts[index].phone = value;
  });
};

// Date picker methods
const toggleDatePicker = () => {
  showDatePicker.value = !showDatePicker.value;
};

const updateDateOfBirth = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  store.updateField("dateOfBirth", `${year}-${month}-${day}`);
  showDatePicker.value = false;
};

const clearDateOfBirth = () => {
  store.updateField("dateOfBirth", "");
  store.setLabelActive("dateOfBirth", false);
};

const getMaxDate = () => {
  const today = new Date();
  today.setFullYear(today.getFullYear() - 11);
  return today;
};

// Event handlers
const handleClickOutside = (event: Event) => {
  const target = event.target as Element;
  if (!target.closest(".relative")) {
    showDatePicker.value = false;
  }
};

// Lifecycle
onMounted(async () => {
  document.addEventListener("click", handleClickOutside);
  if (isEditMode.value) {
    await getTalentSecurity(Number(route.params.id));
    await getTalentEmergencyContacts(Number(route.params.id));
    await getTalentSkillSet(Number(route.params.id));
  }
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>

<template>
  <div class="px-8 space-y-6">
    <!-- Basic Info -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">1. Basic Info</h3>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.firstName"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.firstName }"
              @focus="store.setLabelActive('firstName', true)"
              @blur="
                store.setLabelActive('firstName', false);
                validateField('firstName', form.firstName);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.firstName || form.firstName,
                  'text-red-600': fieldErrors.firstName,
                },
              ]"
            >
              First Name
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.firstName" class="text-sm text-red-600">
              {{ fieldErrors.firstName }}
            </div>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.middleName"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setLabelActive('middleName', true)"
              @blur="store.setLabelActive('middleName', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.middleName || form.middleName,
                },
              ]"
            >
              Middle Name
            </label>
          </div>
          <div class="mt-1 h-5"></div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.lastName"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.lastName }"
              @focus="store.setLabelActive('lastName', true)"
              @blur="
                store.setLabelActive('lastName', false);
                validateField('lastName', form.lastName);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.lastName || form.lastName,
                  'text-red-600': fieldErrors.lastName,
                },
              ]"
            >
              Last Name
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.lastName" class="text-sm text-red-600">
              {{ fieldErrors.lastName }}
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-4 mt-5 md:grid-cols-3">
        <div>
          <div class="flex relative">
            <div class="floating-label-container min-w-[90px]">
              <select
                v-model="form.primaryCountryCode"
                class="floating-select rounded-l-lg min-w-[90px] text-left appearance-none"
                @focus="store.setLabelActive('primaryCountryCode', true)"
                @blur="store.setLabelActive('primaryCountryCode', false)"
              >
                <option value="">Country Code</option>
                <option value="+1">(+1)</option>
                <option value="+52">(+52)</option>
                <option value="+44">(+44)</option>
              </select>
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      labelStates.primaryCountryCode || form.primaryCountryCode,
                  },
                ]"
              >
                Country Code
              </label>
            </div>
            <div class="flex-1 floating-label-container">
              <input
                :value="formatPhoneDisplay(form.primaryContact)"
                type="text"
                maxlength="12"
                placeholder=""
                class="px-4 py-3 w-full bg-white rounded-r-lg border-t border-r border-b border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                :class="{ 'border-red-500': fieldErrors.primaryContact }"
                @input="handlePrimaryPhoneInput"
                @focus="store.setLabelActive('primaryContact', true)"
                @blur="
                  store.setLabelActive('primaryContact', false);
                  validateField('primaryContact', form.primaryContact);
                "
              />
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      labelStates.primaryContact || form.primaryContact,
                    'text-red-600': fieldErrors.primaryContact,
                  },
                ]"
              >
                Phone Number
              </label>
            </div>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.primaryContact" class="text-sm text-red-600">
              {{ fieldErrors.primaryContact }}
            </div>
          </div>
        </div>
        <div>
          <div class="flex relative">
            <div class="floating-label-container min-w-[90px]">
              <select
                v-model="form.secondaryCountryCode"
                class="floating-select rounded-l-lg min-w-[90px] text-left appearance-none"
                @focus="store.setLabelActive('secondaryCountryCode', true)"
                @blur="store.setLabelActive('secondaryCountryCode', false)"
              >
                <option value="">Country Code</option>
                <option value="+1">(+1)</option>
                <option value="+52">(+52)</option>
                <option value="+44">(+44)</option>
              </select>
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      labelStates.secondaryCountryCode ||
                      form.secondaryCountryCode,
                  },
                ]"
              >
                Country Code
              </label>
            </div>
            <div class="flex-1 floating-label-container">
              <input
                :value="formatPhoneDisplay(form.secondaryContact)"
                type="text"
                maxlength="12"
                placeholder=""
                class="px-4 py-3 w-full bg-white rounded-r-lg border-t border-r border-b border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                @input="handleSecondaryPhoneInput"
                @focus="store.setLabelActive('secondaryContact', true)"
                @blur="store.setLabelActive('secondaryContact', false)"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      labelStates.secondaryContact || form.secondaryContact,
                  },
                ]"
              >
                Phone Number
              </label>
            </div>
          </div>
          <div class="mt-1 h-5"></div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.personalEmail"
              type="email"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.personalEmail }"
              @focus="store.setLabelActive('personalEmail', true)"
              @blur="
                store.setLabelActive('personalEmail', false);
                validateField('personalEmail', form.personalEmail);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.personalEmail || form.personalEmail,
                  'text-red-600': fieldErrors.personalEmail,
                },
              ]"
            >
              Personal Email
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.personalEmail" class="text-sm text-red-600">
              {{ fieldErrors.personalEmail }}
            </div>
          </div>
        </div>
      </div>

      <div class="flex gap-4 mt-5">
        <div class="w-full floating-label-container md:w-1/3">
          <select
            v-model="form.gender"
            class="floating-select"
            :class="{ 'border-red-500': fieldErrors.gender }"
            @focus="store.setLabelActive('gender', true)"
            @blur="
              store.setLabelActive('gender', false);
              validateField('gender', form.gender);
            "
          >
            <option value="">Gender</option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
            <option value="Other">Other</option>
          </select>
          <label
            :class="[
              'floating-label',
              {
                'active text-orange-600': labelStates.gender || form.gender,
                'text-red-600': fieldErrors.gender,
              },
            ]"
          >
            Gender
          </label>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.gender" class="text-sm text-red-600">
              {{ fieldErrors.gender }}
            </div>
          </div>
        </div>
        <div class="w-full floating-label-container md:w-1/3">
          <div class="relative">
            <input
              :value="
                form.dateOfBirth
                  ? datePickerFormatter(new Date(form.dateOfBirth))
                  : ''
              "
              type="text"
              readonly
              class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.dateOfBirth }"
              @click="toggleDatePicker"
              @focus="store.setLabelActive('dateOfBirth', true)"
              @blur="
                store.setLabelActive('dateOfBirth', false);
                validateField('dateOfBirth', form.dateOfBirth);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.dateOfBirth || form.dateOfBirth,
                  'text-red-600': fieldErrors.dateOfBirth,
                },
              ]"
            >
              Date of Birth
            </label>
            <!-- Clear button -->
            <button
              v-if="form.dateOfBirth"
              @click.stop="clearDateOfBirth"
              class="absolute right-3 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
            >
              <X :size="16" />
            </button>
            <CalendarComponent
              :is-open="showDatePicker"
              :selected-date="
                form.dateOfBirth ? new Date(form.dateOfBirth) : getMaxDate()
              "
              :max-date="getMaxDate()"
              :min-date="new Date(1970, 0, 1)"
              @select-date="updateDateOfBirth"
              @close="showDatePicker = false"
            />
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.dateOfBirth" class="text-sm text-red-600">
              {{ fieldErrors.dateOfBirth }}
            </div>
          </div>
        </div>
        <div class="w-full floating-label-container md:w-1/3">
          <input
            v-model="form.yearsOfExperience"
            type="number"
            min="0"
            class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
            :class="{ 'border-red-500': fieldErrors.yearsOfExperience }"
            @focus="store.setLabelActive('yearsOfExperience', true)"
            @blur="
              store.setLabelActive('yearsOfExperience', false);
              validateField('yearsOfExperience', form.yearsOfExperience);
            "
          />
          <label
            :class="[
              'floating-label',
              {
                'active text-orange-600':
                  labelStates.yearsOfExperience ||
                  form.yearsOfExperience !== null,
                'text-red-600': fieldErrors.yearsOfExperience,
              },
            ]"
          >
            Years of Experience
          </label>
          <div class="mt-1 h-5">
            <div
              v-if="fieldErrors.yearsOfExperience"
              class="text-sm text-red-600"
            >
              {{ fieldErrors.yearsOfExperience }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Address -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">2. Address</h3>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.address1"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.address1 }"
              @focus="store.setLabelActive('address1', true)"
              @blur="
                store.setLabelActive('address1', false);
                validateField('address1', form.address1);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.address1 || form.address1,
                  'text-red-600': fieldErrors.address1,
                },
              ]"
            >
              Address 1
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.address1" class="text-sm text-red-600">
              {{ fieldErrors.address1 }}
            </div>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.aptUnit"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setLabelActive('aptUnit', true)"
              @blur="store.setLabelActive('aptUnit', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600': labelStates.aptUnit || form.aptUnit,
                },
              ]"
            >
              Apt / Unit #
            </label>
          </div>
          <div class="mt-1 h-5"></div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.address2"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setLabelActive('address2', true)"
              @blur="store.setLabelActive('address2', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.address2 || form.address2,
                },
              ]"
            >
              Address 2
            </label>
          </div>
          <div class="mt-1 h-5"></div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-4 mt-5 md:grid-cols-4">
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.city"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.city }"
              @focus="store.setLabelActive('city', true)"
              @blur="
                store.setLabelActive('city', false);
                validateField('city', form.city);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600': labelStates.city || form.city,
                  'text-red-600': fieldErrors.city,
                },
              ]"
            >
              City
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.city" class="text-sm text-red-600">
              {{ fieldErrors.city }}
            </div>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.state"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.state }"
              @focus="store.setLabelActive('state', true)"
              @blur="
                store.setLabelActive('state', false);
                validateField('state', form.state);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600': labelStates.state || form.state,
                  'text-red-600': fieldErrors.state,
                },
              ]"
            >
              State / Province
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.state" class="text-sm text-red-600">
              {{ fieldErrors.state }}
            </div>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.postalCode"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              :class="{ 'border-red-500': fieldErrors.postalCode }"
              @focus="store.setLabelActive('postalCode', true)"
              @blur="
                store.setLabelActive('postalCode', false);
                validateField('postalCode', form.postalCode);
              "
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.postalCode || form.postalCode,
                  'text-red-600': fieldErrors.postalCode,
                },
              ]"
            >
              Postal Code
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.postalCode" class="text-sm text-red-600">
              {{ fieldErrors.postalCode }}
            </div>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <select
              v-model="form.country"
              class="floating-select"
              :class="{ 'border-red-500': fieldErrors.country }"
              @focus="store.setLabelActive('country', true)"
              @blur="
                store.setLabelActive('country', false);
                validateField('country', form.country);
              "
            >
              <option value="Mexico">Mexico</option>
              <option value="United States">United States</option>
              <option value="Canada">Canada</option>
            </select>
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600': labelStates.country || form.country,
                  'text-red-600': fieldErrors.country,
                },
              ]"
            >
              Country
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.country" class="text-sm text-red-600">
              {{ fieldErrors.country }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Personal Security Info -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">
        3. Personal Security Info
      </h3>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.imms"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setLabelActive('imms', true)"
              @blur="store.setLabelActive('imms', false)"
            />
            <label
              :class="[
                'floating-label',
                { 'active text-orange-600': labelStates.imms || form.imms },
              ]"
            >
              IMMS
            </label>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.curp"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setLabelActive('curp', true)"
              @blur="store.setLabelActive('curp', false)"
            />
            <label
              :class="[
                'floating-label',
                { 'active text-orange-600': labelStates.curp || form.curp },
              ]"
            >
              CURP
            </label>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="form.rfc"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setLabelActive('rfc', true)"
              @blur="store.setLabelActive('rfc', false)"
            />
            <label
              :class="[
                'floating-label',
                { 'active text-orange-600': labelStates.rfc || form.rfc },
              ]"
            >
              RFC
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Emergency Contact -->
    <div
      v-for="(contact, index) in form.emergencyContacts"
      :key="index"
      class="material-section"
    >
      <div class="flex justify-between items-center mb-4">
        <h3 class="mb-2 text-lg font-medium text-gray-900">
          {{
            index === 0
              ? "4. Emergency Contact (1)"
              : `Emergency Contact (${index + 1})`
          }}
        </h3>
        <button
          v-if="index > 0"
          @click="store.removeEmergencyContact(index)"
          class="px-3 py-1 text-sm font-medium text-red-600 rounded-lg transition-all duration-200 hover:text-red-700 hover:bg-red-50"
        >
          Remove
        </button>
      </div>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div>
          <div class="floating-label-container">
            <input
              v-model="contact.firstName"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setContactLabelActive(index, 'firstName', true)"
              @blur="store.setContactLabelActive(index, 'firstName', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    contactLabelStates[index]?.firstName || contact.firstName,
                },
              ]"
            >
              First Name
            </label>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="contact.middleName"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setContactLabelActive(index, 'middleName', true)"
              @blur="store.setContactLabelActive(index, 'middleName', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    contactLabelStates[index]?.middleName || contact.middleName,
                },
              ]"
            >
              Middle Name
            </label>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="contact.lastName"
              type="text"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setContactLabelActive(index, 'lastName', true)"
              @blur="store.setContactLabelActive(index, 'lastName', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    contactLabelStates[index]?.lastName || contact.lastName,
                },
              ]"
            >
              Last Name
            </label>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-4 mt-5 md:grid-cols-3">
        <div>
          <div class="flex relative">
            <div class="floating-label-container min-w-[90px]">
              <select
                v-model="contact.countryCode"
                class="floating-select rounded-l-lg min-w-[90px] text-left appearance-none"
                @focus="store.setContactLabelActive(index, 'countryCode', true)"
                @blur="store.setContactLabelActive(index, 'countryCode', false)"
              >
                <option value="">Country Code</option>
                <option value="+1">(+1)</option>
                <option value="+52">(+52)</option>
                <option value="+44">(+44)</option>
              </select>
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      contactLabelStates[index]?.countryCode ||
                      contact.countryCode,
                  },
                ]"
              >
                Country
              </label>
            </div>
            <div class="flex-1 floating-label-container">
              <input
                :value="formatPhoneDisplay(contact.phone)"
                type="text"
                maxlength="12"
                placeholder=""
                class="px-4 py-3 w-full bg-white rounded-r-lg border-t border-r border-b border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                @input="handleEmergencyPhoneInput($event, index)"
                @focus="store.setContactLabelActive(index, 'phone', true)"
                @blur="store.setContactLabelActive(index, 'phone', false)"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    'active text-orange-600':
                      contactLabelStates[index]?.phone || contact.phone,
                  },
                ]"
              >
                Phone Number
              </label>
            </div>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <input
              v-model="contact.email"
              type="email"
              class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
              @focus="store.setContactLabelActive(index, 'email', true)"
              @blur="store.setContactLabelActive(index, 'email', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    contactLabelStates[index]?.email || contact.email,
                },
              ]"
            >
              Personal Email
            </label>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <select
              v-model="contact.relationship"
              class="floating-select"
              @focus="store.setContactLabelActive(index, 'relationship', true)"
              @blur="store.setContactLabelActive(index, 'relationship', false)"
            >
              <option>Mother</option>
              <option>Father</option>
              <option>Spouse</option>
              <option>Sibling</option>
              <option>Friend</option>
            </select>
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    contactLabelStates[index]?.relationship ||
                    contact.relationship,
                },
              ]"
            >
              Relationship
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-2">
      <button
        @click="store.addEmergencyContact"
        class="flex items-center px-4 py-2 font-medium rounded-lg transition-all duration-200 text-primary-600 hover:text-primary-700 hover:bg-primary-50"
      >
        <Plus :size="16" class="mr-1" />
        Add Other Emergency Contact
      </button>
    </div>

    <!-- Skills Set -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">5. Skills Set</h3>
      <div class="space-y-4">
        <!-- Skills List -->
        <div
          v-if="form.skills && form.skills.length > 0"
          class="flex flex-wrap gap-2 mb-5"
        >
          <div
            v-for="(skill, index) in form.skills"
            :key="index"
            class="flex items-center px-3 py-1 text-sm bg-gray-50 rounded-full border"
          >
            <span class="text-gray-700">{{ skill }}</span>
            <button
              @click="removeSkill(index)"
              class="ml-2 text-red-600 transition-colors hover:text-red-700"
            >
              <X :size="14" />
            </button>
          </div>
        </div>

        <!-- Add Skill Input -->
        <div class="w-full floating-label-container md:w-1/3">
          <input
            v-model="newSkill"
            type="text"
            list="skillOptions"
            class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
            :class="{ 'border-red-500': fieldErrors.skills }"
            @focus="store.setLabelActive('newSkill', true)"
            @blur="
              store.setLabelActive('newSkill', false);
              validateField('skills', form.skills);
            "
            @keydown.enter.prevent="addSkill"
          />
          <datalist id="skillOptions">
            <option value="Customer Support">Customer Support</option>
            <option value="Technical Support">Technical Support</option>
            <option value="Sales">Sales</option>
            <option value="Data Entry">Data Entry</option>
          </datalist>
          <label
            :class="[
              'floating-label',
              {
                'active text-orange-600': labelStates.newSkill || newSkill,
                'text-red-600': fieldErrors.skills,
              },
            ]"
          >
            Add Skill (Type or Select)
          </label>
        </div>

        <!-- Add Skill Button -->
        <button
          @click="addSkill"
          type="button"
          class="px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-lg transition-colors hover:bg-orange-700"
        >
          Add Skill
        </button>

        <!-- Skills Error Message -->
        <div class="h-5">
          <div v-if="fieldErrors.skills" class="text-sm text-red-600">
            {{ fieldErrors.skills }}
          </div>
        </div>
      </div>
    </div>

    <!-- English Proficiency -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">
        6. English Proficiency
      </h3>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div>
          <div class="floating-label-container">
            <select
              v-model="form.writtenEnglish"
              class="floating-select"
              :class="{ 'border-red-500': fieldErrors.writtenEnglish }"
              @focus="store.setLabelActive('writtenEnglish', true)"
              @blur="
                store.setLabelActive('writtenEnglish', false);
                validateField('writtenEnglish', form.writtenEnglish);
              "
            >
              <option value="">Written</option>
              <option>10</option>
              <option>9</option>
              <option>8</option>
              <option>7</option>
              <option>6</option>
            </select>
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.writtenEnglish || form.writtenEnglish,
                  'text-red-600': fieldErrors.writtenEnglish,
                },
              ]"
            >
              Written
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.writtenEnglish" class="text-sm text-red-600">
              {{ fieldErrors.writtenEnglish }}
            </div>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <select
              v-model="form.spokenEnglish"
              class="floating-select"
              :class="{ 'border-red-500': fieldErrors.spokenEnglish }"
              @focus="store.setLabelActive('spokenEnglish', true)"
              @blur="
                store.setLabelActive('spokenEnglish', false);
                validateField('spokenEnglish', form.spokenEnglish);
              "
            >
              <option value="">Spoken</option>
              <option>10</option>
              <option>9</option>
              <option>8</option>
              <option>7</option>
              <option>6</option>
            </select>
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.spokenEnglish || form.spokenEnglish,
                  'text-red-600': fieldErrors.spokenEnglish,
                },
              ]"
            >
              Spoken
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.spokenEnglish" class="text-sm text-red-600">
              {{ fieldErrors.spokenEnglish }}
            </div>
          </div>
        </div>
        <div>
          <div class="floating-label-container">
            <select
              v-model="form.englishType"
              class="floating-select"
              :class="{ 'border-red-500': fieldErrors.englishType }"
              @focus="store.setLabelActive('englishType', true)"
              @blur="
                store.setLabelActive('englishType', false);
                validateField('englishType', form.englishType);
              "
            >
              <option value="">Type</option>
              <option>10</option>
              <option>9</option>
              <option>8</option>
              <option>7</option>
              <option>6</option>
            </select>
            <label
              :class="[
                'floating-label',
                {
                  'active text-orange-600':
                    labelStates.englishType || form.englishType,
                  'text-red-600': fieldErrors.englishType,
                },
              ]"
            >
              Type
            </label>
          </div>
          <div class="mt-1 h-5">
            <div v-if="fieldErrors.englishType" class="text-sm text-red-600">
              {{ fieldErrors.englishType }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Notes -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">7. Notes</h3>
      <div>
        <div class="floating-label-container">
          <textarea
            v-model="form.notes"
            class="resize-none floating-input"
            rows="4"
            @focus="store.setLabelActive('notes', true)"
            @blur="store.setLabelActive('notes', false)"
          ></textarea>
          <label
            :class="[
              'floating-label',
              { 'active text-orange-600': labelStates.notes || form.notes },
            ]"
          >
            Notes From Interview
          </label>
        </div>
      </div>

      <!-- <div class="mt-2"> -->
      <!-- <div class="flex items-center space-x-4">
          <input
            ref="fileInput"
            type="file"
            multiple
            @change="handleFileUpload"
            class="hidden"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          />
          <button
            @click="triggerFileUpload"
            class="flex items-center text-orange-600 border border-orange-300 material-button hover:bg-orange-50"
          >
            <Plus :size="16" class="mr-2" />
            Add Files
          </button>
          <span class="text-sm text-gray-500">Choose File</span>
        </div> -->

      <!-- Display uploaded files -->
      <!-- <div v-if="form.uploadedFiles.length > 0" class="mt-2 space-y-2">
          <div
            v-for="(file, index) in form.uploadedFiles"
            :key="index"
            class="flex justify-between items-center p-4 bg-gray-50 rounded-lg border shadow-sm"
          >
            <span class="text-sm text-gray-700">{{ file.name }}</span>
            <button
              @click="removeFile(index)"
              class="px-3 py-1 text-sm text-red-600 rounded-lg transition-all duration-200 hover:text-red-700 hover:bg-red-50"
            >
              Remove
            </button>
          </div>
        </div> -->
      <!-- </div> -->
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end pt-3">
      <button
        @click="submitForm(isEditMode, talentId)"
        :disabled="isLoading"
        class="text-white material-button bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="isLoading">SUBMITTING...</span>
        <span v-else>SUBMIT</span>
      </button>
    </div>

    <!-- Error Display -->
    <div
      v-if="error"
      class="p-4 mt-4 text-red-700 bg-red-100 rounded-lg border border-red-300"
    >
      {{ error }}
    </div>
  </div>
</template>
