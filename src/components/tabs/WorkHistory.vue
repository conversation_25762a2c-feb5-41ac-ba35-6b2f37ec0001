<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { Calendar, Building2, X, ChevronDown, Edit } from "lucide-vue-next";
import CalendarComponent from "@/components/Calendar.vue";
import { datePickerFormatter } from "@/utils/date-util";
import { formatDate as formatDateUtil } from "@/utils/date-formatting";
import { validateWorkHistoryForm, createEmptyValidationErrors, type ValidationErrors } from "@/utils/validation/work-history-validation";
import { WorkHistoryService } from "@/services/work-history.service";
import { useJobRelatedInfo } from "@/composables/talent/useJobRelatedInfo";
import { useClient } from "@/composables/clients/useClient";
import { ClientForm } from "@/apis/talent/master/client";

const {
  jobRelatedInfoForm,
  labelStates,
  showStartDatePicker,
  showEndDatePicker,
  dropdownStates,
  workDaysOptions,
  pastWorkHistory,
  getFilteredOptions,
  setLabelActive,
  toggleStartDatePicker,
  toggleEndDatePicker,
  updateStartDate,
  updateEndDate,
  clearStartDate,
  clearEndDate,
  selectJobOption,
  handleDropdownInput,
  handleDropdownBlur,
  removeWorkDay,
  addPredefinedWorkDay,
  submitJobForm,
  getTalentJobRelatedInfo,
  updatePastWorkHistory,
} = useJobRelatedInfo();

const { getListOfClients } = useClient()

// Loading and edit states
const isLoading = ref(false);
const isSubmitting = ref(false);
const isEdit = ref(false);
const editIndex = ref(-1);
const clients = ref<ClientForm[]>()

// Form validation
const formErrors = ref<ValidationErrors>(createEmptyValidationErrors());

const editPastWork = (index: number) => {
  if (pastWorkHistory.value && pastWorkHistory.value[index]) {
    const work = pastWorkHistory.value[index];

    // Use service to populate form
    WorkHistoryService.populateFormForEdit(jobRelatedInfoForm.value, work);

    // Ensure clientId is properly set for the dropdown
    if (work.clientId) {
      jobRelatedInfoForm.value.clientId = work.clientId;
    }

    isEdit.value = true;
    editIndex.value = index;
    window.scrollTo(0, 0);
  }
};

// Form validation function
const validateForm = () => {
  const result = validateWorkHistoryForm(jobRelatedInfoForm.value);
  formErrors.value = result.errors;
  return result.isValid;
};

// Clear specific validation error
const clearError = (field: keyof ValidationErrors) => {
  if (formErrors.value[field]) {
    formErrors.value[field] = "";
  }
};

// Click outside handler to close calendars
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;

  // Check if click is outside any calendar component or date input
  const isCalendarClick =
    target.closest(".calendar-component") ||
    target.closest("[data-calendar-trigger]") ||
    target.closest(".floating-label-container");

  if (!isCalendarClick) {
    // Close all calendars
    showStartDatePicker.value = false;
    showEndDatePicker.value = false;
  }
};

// Add event listener on mount
onMounted(async () => {
  isLoading.value = true;
  clients.value = await getListOfClients()
  try {
    await getTalentJobRelatedInfo();
  } catch (error) {
    console.error("Error loading job related info:", error);
  } finally {
    isLoading.value = false;
  }
  document.addEventListener("click", handleClickOutside);
});

// Remove event listener on unmount
onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

const submitWorkHistory = async () => {
  // Validate form before submission
  if (!validateForm()) {
    return;
  }

  isSubmitting.value = true;

  try {
    if (isEdit.value) {
      // Update existing work history
      await updatePastWorkHistory();
    } else {
      // Add new work history
      await submitJobForm();
      pastWorkHistory.value.push(jobRelatedInfoForm.value);
    }
    // Reset form and edit state
    await getTalentJobRelatedInfo();
    resetForm();
    isEdit.value = false;
    editIndex.value = -1;
  } catch (error) {
    console.error("Error submitting form:", error);
  } finally {
    isSubmitting.value = false;
  }
};

const resetForm = () => {
  // Reset form using service
  WorkHistoryService.resetForm(jobRelatedInfoForm.value);

  // Reset validation errors
  formErrors.value = createEmptyValidationErrors();
};

// Helper function to get client name by ID
const getClientNameById = (clientId: number) => {
  if (!clients.value || !clientId) return 'Unknown Client';
  const client = clients.value.find(c => c.id === clientId);
  return client ? client.name : 'Unknown Client';
};

// Use the imported date formatting utility
const formatDate = formatDateUtil;
</script>

<template>
  <div class="relative px-8 space-y-6">
    <!-- Work History Header -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">Job Related Info</h3>

      <!-- Position Type -->
      <div class="mb-2">
        <label class="block mb-2 text-sm font-medium text-gray-700"
          >What type of Position is this Talent committed to?</label
        >
        <div class="flex items-center space-x-6">
          <label class="flex items-center">
            <input
              v-model="jobRelatedInfoForm.positionType"
              type="radio"
              value="FTE"
              class="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
              :class="{ 'border-red-500': formErrors.positionType }"
              @change="clearError('positionType')"
            />
            <span class="ml-2 text-sm text-gray-700">FTE</span>
          </label>
          <label class="flex items-center">
            <input
              v-model="jobRelatedInfoForm.positionType"
              type="radio"
              value="Contractor"
              class="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
              :class="{ 'border-red-500': formErrors.positionType }"
              @change="clearError('positionType')"
            />
            <span class="ml-2 text-sm text-gray-700">Contractor</span>
          </label>
          <label class="flex items-center">
            <input
              v-model="jobRelatedInfoForm.positionType"
              type="radio"
              value="Other"
              class="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
              :class="{ 'border-red-500': formErrors.positionType }"
              @change="clearError('positionType')"
            />
            <span class="ml-2 text-sm text-gray-700">Other</span>
          </label>
        </div>

        <!-- Position Type Validation Error -->
        <div v-if="formErrors.positionType" class="mt-2 text-sm text-red-600">
          {{ formErrors.positionType }}
        </div>
      </div>

      <!-- Campaign, Client Placed, Client Manager, Reporting Department -->
      <div class="mt-5 mb-3">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <!-- Campaign -->
          <div>
            <div class="floating-label-container">
              <div class="relative">
                <input
                  v-model="jobRelatedInfoForm.campaign"
                  type="text"
                  class="pr-8 floating-input"
                  :class="{ 'border-red-500': formErrors.campaign }"
                  @focus="
                    setLabelActive('campaign', true);
                    handleDropdownInput('campaign');
                    clearError('campaign');
                  "
                  @blur="handleDropdownBlur('campaign')"
                  @input="handleDropdownInput('campaign')"
                />
                <label
                  :class="[
                    'floating-label',
                    {
                      active:
                        labelStates.campaign || jobRelatedInfoForm.campaign,
                    },
                  ]"
                >
                  Campaign
                </label>
                <!-- Dropdown arrow -->
                <ChevronDown
                  :size="16"
                  class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                />

                <!-- Custom Dropdown -->
                <div
                  v-if="
                    dropdownStates.campaign &&
                    getFilteredOptions('campaign').length > 0
                  "
                  class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
                >
                  <div
                    v-for="option in getFilteredOptions('campaign')"
                    :key="option"
                    class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                    @click="selectJobOption('campaign', option)"
                  >
                    {{ option }}
                  </div>
                </div>
              </div>
            </div>
            <!-- Campaign Validation Error -->
            <div v-if="formErrors.campaign" class="mt-1 text-sm text-red-600">
              {{ formErrors.campaign }}
            </div>
          </div>

          <!-- Client Placed -->
          <div>
            <div class="floating-label-container">
              <div class="relative">
                <select
                  v-model="jobRelatedInfoForm.clientId"
                  class="px-4 py-3 w-full bg-white rounded-lg border border-gray-300 appearance-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  :class="{ 'border-red-500': formErrors.clientId }"
                  @focus="setLabelActive('clientPlaced', true); clearError('clientId');"
                  @blur="setLabelActive('clientPlaced', false)"
                  @change="clearError('clientId')"
                >n>
                  <option
                    v-for="client in clients"
                    :key="client.id"
                    :value="client.id"
                  >
                    {{ client.name }}
                  </option>
                </select>
                <label
                  :class="[
                    'floating-label',
                    {
                      active:
                        labelStates.clientPlaced || jobRelatedInfoForm.clientId,
                    },
                  ]"
                >
                  Client Placed
                </label>
                <!-- Dropdown arrow -->
                <ChevronDown
                  :size="16"
                  class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                />

                <!-- Client Validation Error -->
                <div
                  v-if="formErrors.clientId"
                  class="mt-1 text-sm text-red-600"
                >
                  {{ formErrors.clientId }}
                </div>
              </div>
            </div>
          </div>

          <!-- Client Manager -->
          <div>
            <div class="floating-label-container">
              <div class="relative">
                <input
                  v-model="jobRelatedInfoForm.clientManager"
                  type="text"
                  class="pr-8 floating-input"
                  :class="{ 'border-red-500': formErrors.clientManager }"
                  @focus="
                    setLabelActive('clientManager', true);
                    handleDropdownInput('clientManager');
                    clearError('clientManager');
                  "
                  @blur="handleDropdownBlur('clientManager')"
                  @input="handleDropdownInput('clientManager')"
                />
                <label
                  :class="[
                    'floating-label',
                    {
                      active:
                        labelStates.clientManager ||
                        jobRelatedInfoForm.clientManager,
                    },
                  ]"
                >
                  Client Manager
                </label>
                <!-- Dropdown arrow -->
                <ChevronDown
                  :size="16"
                  class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                />

                <!-- Custom Dropdown -->
                <div
                  v-if="
                    dropdownStates.clientManager &&
                    getFilteredOptions('clientManager').length > 0
                  "
                  class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
                >
                  <div
                    v-for="option in getFilteredOptions('clientManager')"
                    :key="option"
                    class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                    @click="selectJobOption('clientManager', option)"
                  >
                    {{ option }}
                  </div>
                </div>

                <!-- Client Manager Validation Error -->
                <div
                  v-if="formErrors.clientManager"
                  class="mt-1 text-sm text-red-600"
                >
                  {{ formErrors.clientManager }}
                </div>
              </div>
            </div>
          </div>

          <!-- Reporting Department -->
          <div>
            <div class="floating-label-container">
              <div class="relative">
                <input
                  v-model="jobRelatedInfoForm.reportingDepartment"
                  type="text"
                  class="pr-8 floating-input"
                  :class="{ 'border-red-500': formErrors.reportingDepartment }"
                  @focus="
                    setLabelActive('reportingDepartment', true);
                    handleDropdownInput('reportingDepartment');
                  "
                  @blur="handleDropdownBlur('reportingDepartment')"
                  @input="handleDropdownInput('reportingDepartment')"
                />
                <label
                  :class="[
                    'floating-label',
                    {
                      active:
                        labelStates.reportingDepartment ||
                        jobRelatedInfoForm.reportingDepartment,
                    },
                  ]"
                >
                  Reporting Department
                </label>
                <!-- Dropdown arrow -->
                <ChevronDown
                  :size="16"
                  class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
                />

                <!-- Custom Dropdown -->
                <div
                  v-if="
                    dropdownStates.reportingDepartment &&
                    getFilteredOptions('reportingDepartment').length > 0
                  "
                  class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
                >
                  <div
                    v-for="option in getFilteredOptions('reportingDepartment')"
                    :key="option"
                    class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                    @click="selectJobOption('reportingDepartment', option)"
                  >
                    {{ option }}
                  </div>
                </div>

                <!-- Reporting Department Validation Error -->
                <div
                  v-if="formErrors.reportingDepartment"
                  class="mt-1 text-sm text-red-600"
                >
                  {{ formErrors.reportingDepartment }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Job Details Grid -->
      <div class="grid grid-cols-1 gap-4 mb-3 md:grid-cols-2 lg:grid-cols-3">
        <!-- Role -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="jobRelatedInfoForm.role"
                type="text"
                :class="{ 'border-red-500': formErrors.role }"
                class="pr-8 floating-input"
                @focus="
                  setLabelActive('role', true);
                  handleDropdownInput('role');
                "
                @blur="handleDropdownBlur('role')"
                @input="handleDropdownInput('role')"
              />
              <label
                :class="[
                  'floating-label',
                  { active: labelStates.role || jobRelatedInfoForm.role },
                ]"
              >
                Role
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.role && getFilteredOptions('role').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('role')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectJobOption('role', option)"
                >
                  {{ option }}
                </div>
              </div>

              <!-- Role Validation Error -->
              <div v-if="formErrors.role" class="mt-1 text-sm text-red-600">
                {{ formErrors.role }}
              </div>
            </div>
          </div>
        </div>

        <!-- Title -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="jobRelatedInfoForm.title"
              type="text"
              class="floating-input"
              :class="{ 'border-red-500': formErrors.title }"
              @focus="
                setLabelActive('title', true);
                clearError('title');
              "
              @blur="setLabelActive('title', false)"
            />
            <label
              :class="[
                'floating-label',
                { active: labelStates.title || jobRelatedInfoForm.title },
              ]"
            >
              Title
            </label>
          </div>
          <!-- Title Validation Error -->
          <div v-if="formErrors.title" class="mt-1 text-sm text-red-600">
            {{ formErrors.title }}
          </div>
        </div>

        <!-- Location -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="jobRelatedInfoForm.location"
                type="text"
                :class="{ 'border-red-500': formErrors.location }"
                class="pr-8 floating-input"
                @focus="
                  setLabelActive('location', true);
                  handleDropdownInput('location');
                "
                @blur="handleDropdownBlur('location')"
                @input="handleDropdownInput('location')"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active: labelStates.location || jobRelatedInfoForm.location,
                  },
                ]"
              >
                Location
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.location &&
                  getFilteredOptions('location').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('location')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectJobOption('location', option)"
                >
                  {{ option }}
                </div>
              </div>

              <!-- Location Validation Error -->
              <div v-if="formErrors.location" class="mt-1 text-sm text-red-600">
                {{ formErrors.location }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Employment Dates -->
      <div class="grid grid-cols-1 gap-4 mb-3 md:grid-cols-2">
        <!-- Start Date -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                :value="
                  jobRelatedInfoForm.startDate
                    ? datePickerFormatter(
                        new Date(jobRelatedInfoForm.startDate)
                      )
                    : ''
                "
                type="text"
                readonly
                data-calendar-trigger
                class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                :class="{ 'border-red-500': formErrors.startDate }"
                @click="toggleStartDatePicker"
                @focus="
                  setLabelActive('startDate', true);
                  clearError('startDate');
                "
                @blur="setLabelActive('startDate', false)"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active:
                      labelStates.startDate || jobRelatedInfoForm.startDate,
                  },
                ]"
              >
                Start Date
              </label>
              <!-- Clear button -->
              <button
                v-if="jobRelatedInfoForm.startDate"
                @click.stop="clearStartDate"
                class="absolute right-12 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
              >
                <X :size="16" />
              </button>
              <!-- Calendar icon -->
              <Calendar
                :size="16"
                class="absolute right-4 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />
              <CalendarComponent
                :is-open="showStartDatePicker"
                :selected-date="
                  jobRelatedInfoForm.startDate
                    ? new Date(jobRelatedInfoForm.startDate)
                    : new Date()
                "
                @select-date="updateStartDate"
                @close="showStartDatePicker = false"
              />
            </div>
          </div>
          <!-- Start Date Validation Error -->
          <div v-if="formErrors.startDate" class="mt-1 text-sm text-red-600">
            {{ formErrors.startDate }}
          </div>
        </div>

        <!-- End Date -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                :value="
                  jobRelatedInfoForm.endDate
                    ? datePickerFormatter(new Date(jobRelatedInfoForm.endDate))
                    : ''
                "
                type="text"
                readonly
                data-calendar-trigger
                class="px-4 py-3 pr-20 w-full bg-white rounded-lg border border-gray-300 shadow-sm transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 hover:shadow-md"
                @click="toggleEndDatePicker"
                @focus="setLabelActive('endDate', true)"
                @blur="setLabelActive('endDate', false)"
              />
              <label
                :class="[
                  'floating-label',
                  { active: labelStates.endDate || jobRelatedInfoForm.endDate },
                ]"
              >
                End Date
              </label>
              <!-- Clear button -->
              <button
                v-if="jobRelatedInfoForm.endDate"
                @click.stop="clearEndDate"
                class="absolute right-12 top-1/2 text-gray-400 transition-colors transform -translate-y-1/2 hover:text-gray-600"
              >
                <X :size="16" />
              </button>
              <!-- Calendar icon -->
              <Calendar
                :size="16"
                class="absolute right-4 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />
              <CalendarComponent
                :is-open="showEndDatePicker"
                :selected-date="
                  jobRelatedInfoForm.endDate
                    ? new Date(jobRelatedInfoForm.endDate)
                    : new Date()
                "
                @select-date="updateEndDate"
                @close="showEndDatePicker = false"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Schedule Grid -->
      <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
        <!-- Schedule Start Time -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="jobRelatedInfoForm.shiftStartTime"
              type="time"
              class="floating-input"
              :class="{ 'border-red-500': formErrors.shiftStartTime }"
              @focus="
                setLabelActive('startTime', true);
                clearError('shiftStartTime');
              "
              @blur="setLabelActive('startTime', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active:
                    labelStates.startTime || jobRelatedInfoForm.shiftStartTime,
                },
              ]"
            >
              Start Time
            </label>
          </div>
          <!-- Start Time Validation Error -->
          <div
            v-if="formErrors.shiftStartTime"
            class="mt-1 text-sm text-red-600"
          >
            {{ formErrors.shiftStartTime }}
          </div>
        </div>

        <!-- Schedule End Time -->
        <div>
          <div class="floating-label-container">
            <input
              v-model="jobRelatedInfoForm.shiftEndTime"
              type="time"
              class="floating-input"
              :class="{ 'border-red-500': formErrors.shiftEndTime }"
              @focus="
                setLabelActive('endTime', true);
                clearError('shiftEndTime');
              "
              @blur="setLabelActive('endTime', false)"
            />
            <label
              :class="[
                'floating-label',
                {
                  active:
                    labelStates.endTime || jobRelatedInfoForm.shiftEndTime,
                },
              ]"
            >
              End Time
            </label>
          </div>
          <!-- End Time Validation Error -->
          <div v-if="formErrors.shiftEndTime" class="mt-1 text-sm text-red-600">
            {{ formErrors.shiftEndTime }}
          </div>
        </div>

        <!-- Time Zone -->
        <div>
          <div class="floating-label-container">
            <div class="relative">
              <input
                v-model="jobRelatedInfoForm.timeZone"
                type="text"
                class="pr-8 floating-input"
                :class="{ 'border-red-500': formErrors.timeZone }"
                @focus="
                  setLabelActive('timeZone', true);
                  handleDropdownInput('timeZone');
                "
                @blur="handleDropdownBlur('timeZone')"
                @input="handleDropdownInput('timeZone')"
              />
              <label
                :class="[
                  'floating-label',
                  {
                    active: labelStates.timeZone || jobRelatedInfoForm.timeZone,
                  },
                ]"
              >
                Time Zone
              </label>
              <!-- Dropdown arrow -->
              <ChevronDown
                :size="16"
                class="absolute right-3 top-1/2 text-gray-400 transform -translate-y-1/2 pointer-events-none"
              />

              <!-- Custom Dropdown -->
              <div
                v-if="
                  dropdownStates.timeZone &&
                  getFilteredOptions('timeZone').length > 0
                "
                class="overflow-y-auto absolute z-10 mt-1 w-full max-h-48 bg-white rounded-lg border border-gray-300 shadow-lg"
              >
                <div
                  v-for="option in getFilteredOptions('timeZone')"
                  :key="option"
                  class="px-4 py-2 text-sm cursor-pointer hover:bg-gray-50"
                  @click="selectJobOption('timeZone', option)"
                >
                  {{ option }}
                </div>
              </div>

              <!-- Time Zone Validation Error -->
              <div v-if="formErrors.timeZone" class="mt-2 text-sm text-red-600">
                {{ formErrors.timeZone }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Work Days Selection -->
      <div class="mt-6">
        <h4 class="mb-3 text-sm font-medium text-gray-700">Work Days</h4>

        <!-- Work Days Toggle Buttons/Tags -->
        <div
          class="flex flex-wrap gap-2"
          :class="{
            'p-3 border border-red-500 rounded-lg': formErrors.workDays,
          }"
        >
          <div v-for="day in workDaysOptions" :key="day">
            <!-- Selected Day (Tag) -->
            <div
              v-if="jobRelatedInfoForm.workDays.includes(day)"
              class="flex items-center px-3 py-1 text-sm bg-gray-50 rounded-full border"
            >
              <span class="text-gray-700">{{ day }}</span>
              <button
                @click="removeWorkDay(jobRelatedInfoForm.workDays.indexOf(day))"
                class="ml-2 text-red-600 transition-colors hover:text-red-700"
              >
                <X :size="14" />
              </button>
            </div>

            <!-- Unselected Day (Button) -->
            <button
              v-else
              @click="addPredefinedWorkDay(day)"
              class="px-3 py-1 text-sm rounded-full border border-gray-300 transition-colors hover:bg-gray-50 hover:border-orange-300"
            >
              {{ day }}
            </button>
          </div>
        </div>

        <!-- Work Days Validation Error -->
        <div v-if="formErrors.workDays" class="mt-2 text-sm text-red-600">
          {{ formErrors.workDays }}
        </div>
      </div>

      <!-- Submit Button -->
      <div class="flex justify-end mt-6">
        <button
          @click="submitWorkHistory"
          :disabled="isSubmitting"
          class="text-white bg-gray-600 material-button hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isSubmitting" class="flex items-center">
            <svg
              class="mr-2 w-4 h-4 animate-spin"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {{ isEdit ? "Updating..." : "Submitting..." }}
          </span>
          <span v-else>
            {{ isEdit ? "UPDATE" : "SUBMIT" }}
          </span>
        </button>
      </div>
    </div>

    <!-- Past Work History Section -->
    <div class="material-section">
      <h3 class="mb-2 text-lg font-medium text-gray-900">Past Work History</h3>

      <div role="status" v-if="isLoading">
        <svg
          aria-hidden="true"
          class="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
          viewBox="0 0 100 101"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
            fill="currentColor"
          />
          <path
            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
            fill="currentFill"
          />
        </svg>
        <span class="sr-only">Loading...</span>
      </div>
      <!-- Work History Content -->
      <div v-else>
        <!-- Work History List -->
        <div
          v-if="pastWorkHistory && pastWorkHistory.length > 0"
          class="space-y-4"
        >
          <div
            v-for="(work, index) in pastWorkHistory"
            :key="index"
            class="p-6 material-card"
          >
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <div class="flex items-center mb-2 space-x-3">
                  <div
                    class="flex justify-center items-center w-10 h-10 bg-gray-100 rounded-xl"
                  >
                    <Building2 :size="20" class="text-gray-600" />
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900">
                      {{ work.role }} - {{ work.title }}
                    </h4>
                    <p class="text-sm text-gray-600">
                      {{ getClientNameById(work.clientId) }} -
                      {{
                        work.isFte
                          ? "FTE"
                          : work.isContract
                          ? "Contract"
                          : "Other"
                      }}
                    </p>
                  </div>
                </div>
                <div class="ml-13">
                  <p class="mb-1 text-sm text-gray-500">
                    {{ formatDate(work.startDate) }} -
                    {{ work.endDate ? formatDate(work.endDate) : "Present" }}
                  </p>
                  <p class="text-sm text-gray-600" v-if="work.campaign">
                    {{ work.campaign }} - {{ work.clientManager }}  
                  </p>
                  <p class="text-sm text-gray-600" v-if="work.campaign">
                    {{ work.workDays.join(", ") }} - {{ work.shiftStartTime }} to {{ work.shiftEndTime }} - {{ work.timeZone }}
                  </p>
                </div>
              </div>
              <button
                @click="editPastWork(index)"
                class="p-2 text-gray-400 rounded-lg transition-colors hover:text-blue-600 hover:bg-blue-50"
              >
                <Edit :size="16" />
              </button>
            </div>
          </div>
        </div>

        <!-- No Work History Message -->
        <div v-else class="py-8 text-center">
          <div class="flex flex-col items-center">
            <Building2 :size="48" class="mb-4 text-gray-300" />
            <p class="text-sm text-gray-500">No work history available</p>
            <p class="mt-1 text-xs text-gray-400">
              Submit the form above to add work history
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
