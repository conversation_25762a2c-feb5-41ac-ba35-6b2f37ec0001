import { bpoRelatedInfoApi } from "@/apis/talent/bpo-related-info";
import { ref, computed, watch } from "vue";
import { BpoRelatedInfoCreate, VacationsInfo } from "@/types/bpo-related-info";
import { useRoute } from "vue-router";

export const useBpoRelated = () => {
  const route = useRoute();
  const equipment = ref<
    {
      id: number;
      name: string;
      serialNumber: string;
      model: string;
      isActive: boolean;
    }[]
  >([]);

  const softwareList = ref<
    {
      id: number;
      software: string;
      softwareVersion: string;
      softwareKey: string;
      status: boolean;
    }[]
  >([]);

  const newEquipment = ref({
    name: "",
    serialNumber: "",
    model: "",
    isActive: true,
  });

  const newSoftware = ref({
    software: "",
    softwareKey: "",
    softwareVersion: "",
    status: true,
  });

  const IsEditMode = computed(() => {
    return route.params.id !== undefined && route.params.id !== "add";
  });

  const labelStates = ref({
    // BPO Form fields
    bpoEmail: false,
    reportingDepartment: false,
    bpoManager: false,
    campaign: false,
    location: false,
    isPermanent: false,
    durationInMonths: false,
    position: false,
    bpoRole: false,
    bpoTitle: false,
    shiftStartTime: false,
    shiftEndTime: false,
    startDate: false,
    terminationDate: false,
    reasonForTermination: false,
    contractEndDate: false,
    hoursPerWeek: false,
    hourlyRate: false,
    wageBase: false,
    wageCurrency: false,
    wageFrequency: false,

    // Vacation fields
    paidDaysOff: false,
    vacationsUsed: false,
    remainingVacations: false,
    notes: false,

    // Equipment and Software fields
    equipmentName: false,
    equipmentCode: false,
    equipmentQuantity: false,
    softwareName: false,
    softwareSerial: false,
    softwareStatus: false,
  });

  const showAddEquipmentRow = ref(false);
  const showAddSoftwareRow = ref(false);

  const editingEquipmentIndex = ref<number | null>(null);
  const editingEquipment = ref<any>({});
  const editingSoftwareIndex = ref<number | null>(null);
  const editingSoftware = ref<any>({});

  const bpoRelatedInfoForm = ref<BpoRelatedInfoCreate>({
    talentProfileId: 0,
    reportingManager: "",
    reportingDepartment: "",
    campaign: "",
    bpoEmail: "",
    location: "",
    isPermanent: "yes",
    locationDuration: "",
    position: "",
    role: "",
    title: "",
    shiftStartTime: "",
    shiftEndTime: "",
    isContract: "permanent",
    contractEndDate: "",
    terminationDate: "",
    reasonForTermination: "",
    startDate: "",
    hoursPerWeek: "",
    hourlyRate: "",
    workingWeekDays: [],
    wageBase: "",
    wageCurrency: "",
    wageFrequency: "",
    workDays: [],
    notes: "",
  } as BpoRelatedInfoCreate);

  const vacationsInfo = ref<VacationsInfo>({
    yearNumber: 0,
    talentProfileId: 0,
    usedVacationDays: 0,
    availableVacationDays: 0,
    remainingVacationDays: 0,
  });

  // Vacation form data (separate from main BPO form)
  const vacationForm = ref({
    paidDaysOff: "",
    vacationsUsed: "",
    remainingVacations: "",
  });

  // Work days options
  const workDaysOptions = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  // Vacation calculation logic
  const calculateYearsOfService = () => {
    if (!bpoRelatedInfoForm.value.startDate) return 0;

    const startDate = new Date(bpoRelatedInfoForm.value.startDate);
    const currentDate = new Date();
    const diffTime = Math.abs(currentDate.getTime() - startDate.getTime());
    const diffYears = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365.25));

    return diffYears;
  };

  const calculateVacationDays = (years: number): number => {
    if (years < 1) return 12; // 1st year
    if (years < 2) return 14; // 2nd year
    if (years < 3) return 16; // 3rd year
    if (years < 4) return 18; // 4th year
    if (years < 5) return 20; // 5th year
    return 22; // 6th year and above
  };

  // Computed properties for vacation calculations
  const yearsOfService = computed(() => calculateYearsOfService());
  const totalVacationDays = computed(() =>
    calculateVacationDays(yearsOfService.value)
  );
  const remainingVacationDays = computed(() => {
    const used = parseInt(vacationForm.value.vacationsUsed) || 0;
    const total = totalVacationDays.value;
    const remaining = total - used;
    return Math.max(0, remaining);
  });

  // Watch for changes in vacations used to update remaining vacations
  watch(
    () => vacationForm.value.vacationsUsed,
    (newValue) => {
      const used = parseInt(newValue) || 0;
      const total = totalVacationDays.value;

      // Prevent vacations used from exceeding total vacation days
      if (used > total) {
        vacationForm.value.vacationsUsed = total.toString();
      }

      // Auto-update remaining vacations - ensure it shows 0 when appropriate
      const remaining = Math.max(0, total - used);
      vacationForm.value.remainingVacations = remaining.toString();
    }
  );

  // Watch for changes in start date to update vacation calculations
  watch(
    () => bpoRelatedInfoForm.value.startDate,
    () => {
      const total = totalVacationDays.value;
      const used = parseInt(vacationForm.value.vacationsUsed) || 0;
      const remaining = Math.max(0, total - used);

      vacationForm.value.paidDaysOff = total.toString();
      vacationForm.value.remainingVacations = remaining.toString();
    }
  );

  // Date picker states
  const showBpoStartDatePicker = ref(false);
  const showTerminationDatePicker = ref(false);
  const showContractEndDatePicker = ref(false);

  // Dropdown states for searchable selects
  const dropdownStates = ref({
    reportingDepartment: false,
    bpoManager: false,
    campaign: false,
    clientPlaced: false,
    bpoRole: false,
    role: false,
    location: false,
    workDays: false,
    timeZone: false,
    currency: false,
    frequency: false,
    wageCurrency: false,
    wageFrequency: false,
    selectedWorkDays: false,
  });

  // Dropdown options
  const dropdownOptions = ref({
    reportingDepartment: [
      "Sales",
      "Support",
      "Marketing",
      "Operations",
      "Finance",
    ],
    bpoManager: ["John Smith", "Jane Doe", "Mike Johnson", "Sarah Wilson"],
    campaign: ["Campaign A", "Campaign B", "Campaign C", "Holiday Campaign"],
    clientPlaced: ["Client A", "Client B", "Client C", "Enterprise Client"],
    bpoRole: ["Agent", "Team Lead", "Supervisor", "Manager", "Quality Analyst"],
    role: ["Agent", "Supervisor", "Manager", "Team Lead", "Quality Analyst"],
    location: [
      "WFH",
      "M2-11 Top floor",
      "M2-10 Incubator 1",
      "M2-8 A Incubator",
      "M2-8 Room B",
      "M2-8 Room C",
      "M2-9 Estimators room",
      "M2-7 Room A",
      "M2-7 Room B",
      "M2-7 Room C",
      "M2-4 Room A",
      "M2-4 Room B",
      "M2-4 Room C",
      "M2-3B Training room",
      "M1-14 TMI A",
      "M1-17 TMI B"
    ],
    workDays: ["Monday to Friday", "Monday to Saturday", "Custom"],
    timeZone: ["PST", "EST", "CST", "MST", "GMT"],
    currency: ["Mexican Pesos", "USD", "EUR", "GBP", "CAD"],
    frequency: ["Weekly", "Bi-weekly", "Monthly", "Annually"],
    wageCurrency: ["USD", "Mexican Pesos", "EUR", "GBP", "CAD"],
    wageFrequency: [
      "Hourly",
      "Daily",
      "Weekly",
      "Bi-weekly",
      "Monthly",
      "Annually",
    ],
  });

  // Filtered options based on search input
  const getFilteredOptions = (field: string) => {
    const searchValue = (bpoRelatedInfoForm.value as any)[field] as string;
    if (!searchValue)
      return dropdownOptions.value[field as keyof typeof dropdownOptions.value];

    return dropdownOptions.value[
      field as keyof typeof dropdownOptions.value
    ].filter((option) =>
      option.toLowerCase().includes(searchValue.toLowerCase())
    );
  };

  const setLabelActive = (field: string, active: boolean) => {
    labelStates.value[field as keyof typeof labelStates.value] = active;
  };

  // BPO Info date picker methods
  const toggleBpoStartDatePicker = () => {
    showBpoStartDatePicker.value = !showBpoStartDatePicker.value;
  };

  const toggleTerminationDatePicker = () => {
    showTerminationDatePicker.value = !showTerminationDatePicker.value;
  };

  const updateBpoStartDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    bpoRelatedInfoForm.value.startDate = `${year}-${month}-${day}`;
    showBpoStartDatePicker.value = false;
  };

  const updateTerminationDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    bpoRelatedInfoForm.value.terminationDate = `${year}-${month}-${day}`;
    showTerminationDatePicker.value = false;
  };

  const clearBpoStartDate = () => {
    bpoRelatedInfoForm.value.startDate = "";
    setLabelActive("startDate", false);
  };

  const clearTerminationDate = () => {
    bpoRelatedInfoForm.value.terminationDate = "";
    setLabelActive("terminationDate", false);
  };

  // Contract end date methods
  const toggleContractEndDatePicker = () => {
    showContractEndDatePicker.value = !showContractEndDatePicker.value;
  };

  const updateContractEndDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    bpoRelatedInfoForm.value.contractEndDate = `${year}-${month}-${day}`;
    showContractEndDatePicker.value = false;
  };

  const clearContractEndDate = () => {
    bpoRelatedInfoForm.value.contractEndDate = "";
    setLabelActive("contractEndDate", false);
  };

  // Work days functions
  const removeWorkDay = (index: number) => {
    if (bpoRelatedInfoForm.value.workingWeekDays.length > index) {
      bpoRelatedInfoForm.value.workingWeekDays.splice(index, 1);
    }
  };

  const addPredefinedWorkDay = (day: string) => {
    if (!bpoRelatedInfoForm.value.workingWeekDays.includes(day)) {
      bpoRelatedInfoForm.value.workingWeekDays.push(day);
    }
  };

  const selectBpoOption = (field: string, option: string) => {
    (bpoRelatedInfoForm.value as any)[field] = option;
    dropdownStates.value[field as keyof typeof dropdownStates.value] = false;
    setLabelActive(field, false);
  };

  const handleDropdownInput = (field: string) => {
    dropdownStates.value[field as keyof typeof dropdownStates.value] = true;
  };

  const handleDropdownBlur = (field: string) => {
    setTimeout(() => {
      setLabelActive(field, false);
      dropdownStates.value[field as keyof typeof dropdownStates.value] = false;
    }, 150);
  };

  const submitEquipment = async (masterEquipmentId: number, talentProfileId: number, isActive: boolean) => {
    await bpoRelatedInfoApi.createEquipment({masterEquipmentId, talentProfileId, isActive});
    showAddEquipmentRow.value = false;
    await getTalentEquipment();
  };

  const getTalentEquipment = async () => {
    try {
      const id = Number.parseInt(route.params.id as string);
      const response = await bpoRelatedInfoApi.getEquipment(id);
      equipment.value = response.data;
    } catch (err) {
      alert("Failed to fetch talent information");
    }
  };

  const getAvailableEquipment = async () => {
    try {
      const response = await bpoRelatedInfoApi.geetAvaialbleEquipment();
      return response.data;
    } catch (err) {
      alert("Failed to fetch talent information");
    }
  };

  const updateEquipment = async (index: number) => {
    const equipmentData = equipment.value[index];
    await bpoRelatedInfoApi.updateEquipment(equipmentData.id, { isActive: !equipmentData.isActive });
    await getTalentEquipment();
  };

  const submitSoftware = async () => {
    if (
      newSoftware.value.software &&
      newSoftware.value.softwareKey &&
      newSoftware.value.status
    ) {
      await bpoRelatedInfoApi.createSoftware({
        ...newSoftware.value,
        talentProfileId: Number.parseInt(route.params.id as string),
      });
      showAddSoftwareRow.value = false;
      await getTalentSoftware();
    }
  };

  const getTalentSoftware = async () => {
    try {
      const id = Number.parseInt(route.params.id as string);
      const response = await bpoRelatedInfoApi.getSoftware(id);
      softwareList.value = response.data;
    } catch (err) {
      alert("Failed to fetch talent information");
    }
  };

  const updateSoftware = async (index: number) => { 
    if (editingSoftwareIndex.value !== null) {
      softwareList.value[index] = { ...editingSoftware.value };
    }
    await bpoRelatedInfoApi.updateSoftware({
      ...softwareList.value[index],
      talentProfileId: Number.parseInt(route.params.id as string),
    });
    showAddSoftwareRow.value = false;
    editingSoftwareIndex.value = null;
    await getTalentSoftware();
  };

  const addEquipmentRow = () => {
    showAddEquipmentRow.value = true;
  };

  const addSoftwareRow = () => {
    showAddSoftwareRow.value = true;
  };

  const getTalentProfileBpoRelatedInfo = async () => {
    try {
      const id = Number.parseInt(route.params.id as string);
      const response = await bpoRelatedInfoApi.getByTalentId(id);
      if (!response.data) {
        return;
      }
      bpoRelatedInfoForm.value = response.data;
      bpoRelatedInfoForm.value.isContract = "permanent";
      if (bpoRelatedInfoForm.value.contractEndDate) {
        showContractEndDatePicker.value = true;
        bpoRelatedInfoForm.value.isContract = "contract";
      }
      bpoRelatedInfoForm.value.isLocationPermanent = true;
      if (bpoRelatedInfoForm.value.locationDuration) {
        bpoRelatedInfoForm.value.isLocationPermanent = false;
      }
    } catch (err) {
      alert("Failed to fetch talent information");
    }
  };

  const submitForm = async () => {
    await bpoRelatedInfoApi.create({
      ...bpoRelatedInfoForm.value,
      talentProfileId: Number.parseInt(route.params.id as string),
      locationDuration: bpoRelatedInfoForm.value.isLocationPermanent
        ? undefined
        : bpoRelatedInfoForm.value.locationDuration,
    });
  };

  const getTalentProfileVacationsInfo = async () => {
    try {
      const id = Number.parseInt(route.params.id as string);
      const response = await bpoRelatedInfoApi.getVacationMapping(id);
      if (!response.data) {
        return;
      }
      vacationsInfo.value = response.data;
      vacationForm.value.vacationsUsed =
        response.data.usedVacationDays.toString();
    } catch (err) {
      alert("Failed to fetch talent information");
    }
  };

  const submitVacations = async () => {
    vacationsInfo.value.talentProfileId = Number.parseInt(
      route.params.id as string
    );
    vacationsInfo.value.yearNumber = yearsOfService.value;
    vacationsInfo.value.availableVacationDays = totalVacationDays.value;
    vacationsInfo.value.remainingVacationDays = remainingVacationDays.value;
    vacationsInfo.value.usedVacationDays =
      parseInt(vacationForm.value.vacationsUsed) || 0;
    await bpoRelatedInfoApi.createVacationMapping({
      ...vacationsInfo.value,
      talentProfileId: Number.parseInt(route.params.id as string),
    });
  };

  const preventAtSymbol = (event: KeyboardEvent) => {
    if (event.key === "@") {
      event.preventDefault();
    }
  };

  return {
    // State
    labelStates,
    vacationForm,
    bpoRelatedInfoForm,
    IsEditMode,

    // Date picker states
    showBpoStartDatePicker,
    showTerminationDatePicker,
    showContractEndDatePicker,

    // Dropdown states
    dropdownStates,

    // Equipment and Software
    showAddEquipmentRow,
    showAddSoftwareRow,
    newEquipment,
    newSoftware,
    equipment,
    softwareList,

    // Work days
    workDaysOptions,

    // Vacation calculations
    yearsOfService,
    totalVacationDays,
    remainingVacationDays,
    editingEquipmentIndex,
    editingEquipment,
    editingSoftwareIndex,
    editingSoftware,

    // Methods
    setLabelActive,
    getFilteredOptions,

    // BPO date picker methods
    toggleBpoStartDatePicker,
    updateBpoStartDate,
    clearBpoStartDate,
    toggleTerminationDatePicker,
    updateTerminationDate,
    clearTerminationDate,
    toggleContractEndDatePicker,
    updateContractEndDate,
    clearContractEndDate,

    // Dropdown methods
    selectBpoOption,
    handleDropdownInput,
    handleDropdownBlur,

    // Equipment and Software methods
    submitSoftware,
    getTalentEquipment,
    addEquipmentRow,
    addSoftwareRow,
    submitEquipment,
    updateEquipment,
    updateSoftware,
    getTalentSoftware,

    // Work day methods
    removeWorkDay,
    addPredefinedWorkDay,

    // API methods
    getTalentProfileBpoRelatedInfo,
    submitForm,
    submitVacations,
    getTalentProfileVacationsInfo,
    getAvailableEquipment,

    // Utility methods
    preventAtSymbol,
  };
};
