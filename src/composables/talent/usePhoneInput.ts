
export const usePhoneInput = () => {
  const formatPhoneDisplay = (phone: string) => {
    if (!phone) return ''
    const cleaned = phone.replace(/\D/g, '')
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
    }
    return cleaned
  }

  const handlePhoneInput = (event: Event, callback: (value: string) => void) => {
    const input = event.target as HTMLInputElement
    const cleaned = input.value.replace(/\D/g, '')
    const limited = cleaned.substring(0, 10)
    callback(limited)
    input.value = formatPhoneDisplay(limited)
  }

  return {
    formatPhoneDisplay,
    handlePhoneInput,
  }
}
