import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { UserPermissions, ModulePermission, ModuleId, PermissionType } from '@/types/permissions'
import { settingsApi } from '@/apis/settings/settings'

export const usePermissionsStore = defineStore('permissions', () => {
  // State
  const userPermissions = ref<UserPermissions | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isSuperuser = computed(() => {
    return userPermissions.value?.is_superuser || false
  })

  const userRole = computed(() => {
    return userPermissions.value?.permissions || null
  })

  const modulePermissions = computed(() => {
    // If superuser, return empty array since they have all permissions
    if (isSuperuser.value) {
      return []
    }
    return userPermissions.value?.permissions?.module || []
  })

  // Actions
  const setUserPermissions = (permissions: UserPermissions) => {
    userPermissions.value = permissions
    error.value = null
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (errorMessage: string) => {
    error.value = errorMessage
  }

  const clearPermissions = () => {
    userPermissions.value = null
    error.value = null
  }

  // Fetch user permissions from API
  const fetchUserPermissions = async () => {
    try {
      setLoading(true)
      setError("Loading permissions...")

      const response = await settingsApi.getUserPermissions()

      if (response.status_code === 200 && response.data) {
        setUserPermissions(response.data)
        return response.data
      } else {
        throw new Error(response.message || 'Failed to fetch user permissions')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user permissions'
      setError(errorMessage)
      console.error('Error fetching user permissions:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // Check if user has specific permission for a module
  const hasPermission = (moduleId: ModuleId, permissionType: PermissionType): boolean => {
    // Superusers have all permissions
    if (isSuperuser.value) {
      return true
    }

    // If no permissions object exists and not superuser, deny access
    if (!userPermissions.value?.permissions?.module) {
      return false
    }

    // Find the module permission
    const modulePermission = modulePermissions.value.find(
      (module: ModulePermission) => module.module_id === moduleId
    )

    if (!modulePermission) {
      return false
    }

    // Check the specific permission
    return modulePermission[permissionType] || false
  }

  // Check if user can view a module
  const canView = (moduleId: ModuleId): boolean => {
    return hasPermission(moduleId, 'view')
  }

  // Check if user can edit in a module
  const canEdit = (moduleId: ModuleId): boolean => {
    return hasPermission(moduleId, 'edit')
  }

  // Check if user can create in a module
  const canCreate = (moduleId: ModuleId): boolean => {
    return hasPermission(moduleId, 'create')
  }

  // Check if user can list/access a module
  const canList = (moduleId: ModuleId): boolean => {
    return hasPermission(moduleId, 'list')
  }

  // Check if user can access a module (either view or list permission)
  const canAccess = (moduleId: ModuleId): boolean => {
    return isSuperuser.value || canView(moduleId) || canList(moduleId)
  }

  // Get all accessible modules
  const getAccessibleModules = (): ModuleId[] => {
    if (isSuperuser.value) {
      // Superusers can access all modules
      return Object.values(ModuleId).filter(id => typeof id === 'number') as ModuleId[]
    }

    // If no permissions object exists, return empty array
    if (!userPermissions.value?.permissions?.module) {
      return []
    }

    return modulePermissions.value
      .filter((module: ModulePermission) => module.view || module.list)
      .map((module: ModulePermission) => module.module_id as ModuleId)
  }

  // Check multiple permissions at once
  const hasAnyPermission = (moduleId: ModuleId, permissions: PermissionType[]): boolean => {
    return permissions.some(permission => hasPermission(moduleId, permission))
  }

  // Check if user has all specified permissions
  const hasAllPermissions = (moduleId: ModuleId, permissions: PermissionType[]): boolean => {
    return permissions.every(permission => hasPermission(moduleId, permission))
  }

  // Get user's permissions for a specific module
  const getModulePermissions = (moduleId: ModuleId): ModulePermission | null => {
    if (isSuperuser.value) {
      // Superusers have all permissions
      return {
        module_id: moduleId,
        view: true,
        edit: true,
        create: true,
        list: true
      }
    }

    if (!userPermissions.value?.permissions?.module) {
      return null
    }

    return modulePermissions.value.find(
      (module: ModulePermission) => module.module_id === moduleId
    ) || null
  }

  // Check if user has any talent-related permissions
  const hasAnyTalentPermission = (): boolean => {
    if (isSuperuser.value) {
      return true
    }

    if (!userPermissions.value?.permissions?.module) {
      return false
    }

    // Check if user has any permission (view, edit, create, list) for talent module
    const talentPermission = modulePermissions.value.find(
      (module: ModulePermission) => module.module_id === ModuleId.TALENT
    )

    if (!talentPermission) {
      return false
    }

    return talentPermission.view || talentPermission.edit || talentPermission.create || talentPermission.list
  }

  return {
    // State
    userPermissions,
    isLoading,
    error,

    // Getters
    isSuperuser,
    userRole,
    modulePermissions,

    // Actions
    setUserPermissions,
    setLoading,
    setError,
    clearPermissions,
    fetchUserPermissions,

    // Permission checks
    hasPermission,
    canView,
    canEdit,
    canCreate,
    canList,
    canAccess,
    getAccessibleModules,
    hasAnyPermission,
    hasAllPermissions,
    getModulePermissions,
    hasAnyTalentPermission
  }
})
