<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Calendar, Plus, ChevronDown, Settings, LogOut } from 'lucide-vue-next'
import Sidebar from '@/components/Sidebar.vue'
import CalendarComponent from '@/components/Calendar.vue'
import { monthYearFormat } from '@/utils/date-util.ts'
import { getBackgroundColor, getInitials } from '@/utils/image-background'
import dayjs from 'dayjs'
import { usePermissions } from '@/composables/permissions/usePermissions'

const route = useRoute()
const router = useRouter()

const sidebarCollapsed = ref(false)
const selectedDate = ref(new Date())
const selectedMonth = ref(new Date())
const showDatePicker = ref(false)
const showMonthPicker = ref(false)
const showProfileMenu = ref(false)

const { initializePermissions } = usePermissions()

// Check if current page is a list page (should show content header)
const isListPage = computed(() => {
  const routeName = route.name as string
  return routeName === 'TalentList' || routeName === 'UsersList' || routeName === 'Roles' || routeName === 'Clients' || routeName === 'Equipment' || routeName === 'HistoryLog'
})

const getActiveMenuItem = () => {
  const routeName = route.name as string
  if (routeName === 'Settings') return '' // No menu item highlighted for Settings
  if (routeName?.includes('Talent')) return 'Talent'
  if (routeName?.includes('User')) return 'Users'
  if (routeName?.includes('Role')) return 'Roles'
  if (routeName?.includes('Client')) return 'Clients'
  if (routeName?.includes('Equipment')) return 'Equipment'
  if (routeName === 'HistoryLog') return 'History Log'
  return 'Dashboard'
}

const getPageTitle = () => {
  const routeName = route.name as string
  if (routeName?.includes('Talent')) return 'Talent'
  if (routeName === 'Roles') return 'Roles'
  if (routeName === 'Clients') return 'Clients'
  if (routeName === 'Equipment') return 'Equipment'
  if (routeName === 'HistoryLog') return 'History Log'
  return 'Users'
}

const handleAddButton = () => {
  const activeItem = getActiveMenuItem()
  if (activeItem === 'Talent') {
    router.push('/talent/add')
  } else if (activeItem === 'Clients') {
    router.push('/clients/add')
  } else if (activeItem === 'Users') {
    router.push('/users/add')
  } else if (activeItem === 'Equipment') {
    router.push('/equipment/add')
  } else if (activeItem === 'Roles') {
    router.push('/roles/add')
  }
}

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const toggleDatePicker = () => {
  showDatePicker.value = !showDatePicker.value
  showMonthPicker.value = false
  showProfileMenu.value = false
}

const toggleMonthPicker = () => {
  showMonthPicker.value = !showMonthPicker.value
  showDatePicker.value = false
  showProfileMenu.value = false
}

const toggleProfileMenu = () => {
  showProfileMenu.value = !showProfileMenu.value
  showDatePicker.value = false
  showMonthPicker.value = false
}

const updateSelectedDate = (date: Date) => {
  selectedDate.value = date
  showDatePicker.value = false
}

const updateSelectedMonth = (date: Date) => {
  selectedMonth.value = date
  showMonthPicker.value = false
}

const handleLogout = () => {
  localStorage.removeItem('accessToken')
  localStorage.removeItem('name')
  localStorage.removeItem('email')
  localStorage.removeItem('isSuperuser')
  localStorage.removeItem('pic')
  localStorage.removeItem('phone')
  router.push('/')
  showProfileMenu.value = false
}

const handleSettings = () => {
  router.push('/settings')
  showProfileMenu.value = false
}

const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.relative')) {
    showDatePicker.value = false
    showMonthPicker.value = false
    showProfileMenu.value = false
  }
}

// Get user data from localStorage
const getUserName = () => {
  return localStorage.getItem('name') || 'User'
}

const getUserProfilePic = () => {
  const pic = localStorage.getItem('pic') || ''
  return pic ? `data:image/jpeg;base64,${pic}` : ''
}

// Reactive user data
const userName = computed(() => getUserName())
const userProfilePic = computed(() => getUserProfilePic())

onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  await initializePermissions()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>


<template>
  <!-- Show login page if not logged in -->
  <router-view v-if="$route.name === 'Login'" />
  
  <!-- Main Dashboard Layout -->
  <div v-else class="flex h-screen bg-gray-50">
    <!-- Sidebar -->
    <Sidebar 
      :collapsed="sidebarCollapsed" 
      :active-item="getActiveMenuItem()"
      @toggle-sidebar="toggleSidebar"
    />

    <!-- Main Content -->
    <div class="flex flex-col flex-1 min-w-0">
      <!-- Header -->
      <header class="px-6 py-4 bg-white border-b border-gray-200">
        <div class="flex justify-between items-center">
          <!-- Breadcrumb Navigation -->
          <div class="flex items-center space-x-2">
          </div>

          <!-- User Profile -->
          <div class="relative">
            <button
              @click="toggleProfileMenu"
              class="flex items-center px-3 py-2 space-x-3 rounded-lg transition-colors hover:bg-gray-50"
            >
              <!-- Profile Picture or Initials -->
              <img
                v-if="userProfilePic"
                :src="userProfilePic"
                :alt="userName"
                class="object-cover flex-shrink-0 w-8 h-8 rounded-full"
              />
              <div
                v-else
                :class="[
                  'flex justify-center items-center w-8 h-8 rounded-full border-2 border-gray-200 font-medium text-xs flex-shrink-0',
                  getBackgroundColor(userName),
                ]"
              >
                {{ getInitials(userName) }}
              </div>
              <span class="text-sm font-medium text-gray-700">{{ userName }}</span>
              <ChevronDown :size="16" class="text-gray-500" />
            </button>
            
            <!-- Profile Dropdown -->
            <div v-if="showProfileMenu" class="absolute right-0 top-full z-50 mt-2 w-48 bg-white rounded-lg border border-gray-200 shadow-lg">
              <div class="py-1">
                <button 
                  @click="handleSettings"
                  class="flex items-center px-4 py-2 w-full text-sm text-gray-700 hover:bg-gray-50"
                >
                  <Settings :size="16" class="mr-3" />
                  Settings
                </button>
                <button 
                  @click="handleLogout"
                  class="flex items-center px-4 py-2 w-full text-sm text-red-600 hover:bg-red-50"
                >
                  <LogOut :size="16" class="mr-3" />
                  Log Out
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Content Header with Title and Date Pickers (Only for List Pages) -->
      <div v-if="isListPage" class="px-6 py-4 bg-white border-b border-gray-200">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">{{ getPageTitle() }}</h1>
            <p class="text-sm text-gray-500">{{ dayjs().format('dddd, D MMMM YYYY') }}</p>
          </div>

          <div class="flex items-center space-x-4">
            <!-- Date Pickers -->
            <div v-if="isListPage" class="relative calendar-container">
              <button 
                @click="toggleMonthPicker"
                class="flex items-center px-4 py-2 bg-white rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
              >
                <Calendar :size="16" class="mr-2 text-gray-500" />
                <span class="text-sm font-medium">{{ monthYearFormat(selectedMonth) }}</span>
              </button>
              <CalendarComponent 
                :is-open="showMonthPicker"
                :selected-date="selectedMonth"
                @select-date="updateSelectedMonth"
                @close="showMonthPicker = false"
              />
            </div>

            <div v-if="isListPage" class="relative calendar-container">
              <button 
                @click="toggleDatePicker"
                class="flex relative items-center px-4 py-2 bg-white rounded-lg border border-gray-300 transition-colors hover:bg-gray-50"
              >
                <Calendar :size="16" class="mr-2 text-gray-500" />
                <span class="text-sm font-medium">{{ monthYearFormat(selectedDate) }}</span>
              </button>
              <CalendarComponent 
                :is-open="showDatePicker"
                :selected-date="selectedDate"
                :position="'right'"
                @select-date="updateSelectedDate"
                @close="showDatePicker = false"
              />
            </div>

            <!-- Add Button -->
            <button 
              @click="handleAddButton"
              class="flex justify-center items-center w-10 h-10 text-white bg-gray-800 rounded-lg shadow-sm transition-colors hover:bg-gray-900"
            >
              <Plus :size="20" />
            </button>
          </div>
        </div>
      </div>

      <!-- Main Content Area -->
      <main class="overflow-auto flex-1 p-6 smooth-scroll">
        <router-view />
      </main>
    </div>
  </div>
</template>
