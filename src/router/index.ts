import { createRouter, createWebHistory } from 'vue-router'
import { usePermissionsStore } from '@/store/permissions/usePermissionsStore'
import { ModuleId } from '@/types/permissions'

const routes = [
  {
    path: '/',
    name: 'Login',
    component: () => import('@/views/LoginPage.vue'),
    meta: { title: 'Login - BPO Management' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/DashboardView.vue'),
    meta: { title: 'Dashboard - BPO Management', requiresAuth: true }
  },
  {
    path: '/talent',
    name: 'TalentList',
    component: () => import('@/views/talent/TalentListView.vue'),
    meta: { title: 'Talent - BPO Management', requiresAuth: true }
  },
  {
    path: '/talent/add',
    name: 'AddTalent',
    component: () => import('@/views/talent/AddTalentPage.vue'),
    meta: { title: 'Add Talent - BPO Management', requiresAuth: true }
  },
  {
    path: '/talent/:id',
    name: 'TalentProfile',
    component: () => import('@/views/talent/AddTalentPage.vue'),
    meta: { title: 'Talent Profile - BPO Management', requiresAuth: true },
    props: true,
    children: [
      {
        path: '',
        name: 'TalentProfileDefault',
        redirect: 'profile'
      },
      {
        path: 'profile',
        name: 'TalentProfileTab',
        component: () => import('@/views/talent/AddTalentPage.vue'),
        meta: { tab: 'profile' }
      },
      {
        path: 'banking',
        name: 'TalentBankingTab',
        component: () => import('@/views/talent/AddTalentPage.vue'),
        meta: { tab: 'banking' }
      },
      {
        path: 'documents',
        name: 'TalentDocumentsTab',
        component: () => import('@/views/talent/AddTalentPage.vue'),
        meta: { tab: 'documents' }
      },
      {
        path: 'work-history',
        name: 'TalentWorkHistoryTab',
        component: () => import('@/views/talent/AddTalentPage.vue'),
        meta: { tab: 'work-history' }
      },
      {
        path: 'health',
        name: 'TalentHealthTab',
        component: () => import('@/views/talent/AddTalentPage.vue'),
        meta: { tab: 'health' }
      },
      {
        path: 'equipment',
        name: 'equipmentAndSoftware',
        component: () => import('@/views/talent/AddTalentPage.vue'),
        meta: { tab: 'equipment' }
      },
      {
        path: 'third-party',
        name: 'TalentThirdPartyTab',
        component: () => import('@/views/talent/AddTalentPage.vue'),
        meta: { tab: 'third-party' }
      },
      {
        path: 'job-related',
        name: 'TalentJobRelatedTab',
        component: () => import('@/views/talent/AddTalentPage.vue'),
        meta: { tab: 'job-related' }
      },
      {
        path: 'history-log',
        name: 'TalentHistoryLogTab',
        component: () => import('@/views/talent/AddTalentPage.vue'),
        meta: { tab: 'history-log' }
      }
    ]
  },
  {
    path: '/users',
    name: 'UsersList',
    component: () => import('@/views/users/UsersListView.vue'),
    meta: { title: 'Users - BPO Management', requiresAuth: true }
  },
  {
    path: '/users/add',
    name: 'AddUser',
    component: () => import('@/views/users/AddUserView.vue'),
    meta: { title: 'Add User - BPO Management', requiresAuth: true }
  },
  {
    path: '/users/:id/edit',
    name: 'EditUser',
    component: () => import('@/views/users/AddUserView.vue'),
    meta: { title: 'Edit User - BPO Management', requiresAuth: true },
    props: true
  },
  {
    path: '/roles',
    name: 'Roles',
    component: () => import('@/views/roles/RolesListView.vue'),
    meta: { title: 'Roles - BPO Management', requiresAuth: true }
  },
  {
    path: '/roles/add',
    name: 'AddRole',
    component: () => import('@/views/roles/AddRoleView.vue'),
    meta: { title: 'Add Role - BPO Management', requiresAuth: true }
  },
  {
    path: '/roles/:id/edit',
    name: 'EditRole',
    component: () => import('@/views/roles/AddRoleView.vue'),
    meta: { title: 'Edit Role - BPO Management', requiresAuth: true },
    props: true
  },
  {
    path: '/clients',
    name: 'Clients',
    component: () => import('@/views/clients/ClientsListView.vue'),
    meta: { title: 'Clients - BPO Management', requiresAuth: true }
  },
  {
    path: '/clients/add',
    name: 'AddClient',
    component: () => import('@/views/clients/AddEditClientView.vue'),
    meta: { title: 'Add Client - BPO Management', requiresAuth: true }
  },
  {
    path: '/clients/:id',
    name: 'EditClient',
    component: () => import('@/views/clients/AddEditClientView.vue'),
    meta: { title: 'Edit Client - BPO Management', requiresAuth: true },
    props: true
  },
  {
    path: '/equipment',
    name: 'Equipment',
    component: () => import('@/views/equipment/EquipmentListView.vue'),
    meta: { title: 'Equipment - BPO Management', requiresAuth: true }
  },
  {
    path: '/equipment/add',
    name: 'AddEquipment',
    component: () => import('@/views/equipment/AddEditEquipmentView.vue'),
    meta: { title: 'Add Equipment - BPO Management', requiresAuth: true }
  },
  {
    path: '/equipment/:id',
    name: 'EditEquipment',
    component: () => import('@/views/equipment/AddEditEquipmentView.vue'),
    meta: { title: 'Edit Equipment - BPO Management', requiresAuth: true },
    props: true
  },
  {
    path: '/history-log',
    name: 'HistoryLog',
    component: () => import('@/views/HistoryLogView.vue'),
    meta: { title: 'History Log - BPO Management', requiresAuth: true }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/SettingsView.vue'),
    meta: { title: 'Settings - BPO Management', requiresAuth: true }
  },
  {
    path: '/403',
    name: 'Unauthorized',
    component: () => import('@/views/errors/UnauthorizedPage.vue'),
    meta: { title: '403 - Access Forbidden', requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue'),
    meta: { title: '404 - Page Not Found' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Global navigation guard
router.beforeEach(async (to, _, next) => {
  // Update page title
  if (to.meta.title) {
    document.title = to.meta.title as string
  }

  // Check authentication
  const accessToken = localStorage.getItem('accessToken')
  const isAuthenticated = !!accessToken

  // If route requires auth and user is not authenticated, redirect to login
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/')
    return
  }

  // If user is authenticated and trying to access login, redirect to dashboard
  if (to.name === 'Login' && isAuthenticated) {
    next('/dashboard')
    return
  }

  // Permission checks for authenticated users
  if (isAuthenticated && to.meta.requiresAuth) {
    const permissionsStore = usePermissionsStore()

    // Ensure permissions are loaded
    if (!permissionsStore.userPermissions) {
      try {
        await permissionsStore.fetchUserPermissions()
      } catch (error) {
        console.error('Failed to load permissions:', error)
        // Continue without permissions - individual components will handle this
      }
    }

    // Check specific route permissions
    const routePermissions = getRoutePermissions(to.name as string)
    if (routePermissions) {
      const hasPermission = checkRoutePermission(permissionsStore, routePermissions)

      if (!hasPermission) {
        next('/403')
        return
      }
    }
    // If routePermissions is null, the route is accessible to all authenticated users
  }

  next()
})

// Helper function to get required permissions for a route
function getRoutePermissions(routeName: string): { moduleId: ModuleId; permission: string } | null {
  const permissionMap: Record<string, { moduleId: ModuleId; permission: string }> = {
    // Dashboard is accessible to all authenticated users
    'Dashboard': null, // No specific permission required
    'TalentList': { moduleId: ModuleId.TALENT, permission: 'any' }, // Changed to 'any' for talent list
    'AddTalent': { moduleId: ModuleId.TALENT, permission: 'create' },
    'TalentProfile': { moduleId: ModuleId.TALENT, permission: 'any' }, // Changed to 'any' for talent profile
    'TalentProfileTab': { moduleId: ModuleId.TALENT, permission: 'any' },
    'TalentBankingTab': { moduleId: ModuleId.TALENT, permission: 'any' },
    'TalentDocumentsTab': { moduleId: ModuleId.TALENT, permission: 'any' },
    'TalentWorkHistoryTab': { moduleId: ModuleId.TALENT, permission: 'any' },
    'TalentJobRelatedTab': { moduleId: ModuleId.TALENT, permission: 'any' },
    'TalentEquipmentTab': { moduleId: ModuleId.TALENT, permission: 'any' },
    'TalentThirdPartyTab': { moduleId: ModuleId.TALENT, permission: 'any' },
    'TalentHealthTab': { moduleId: ModuleId.TALENT, permission: 'any' },
    'TalentHistoryLogTab': { moduleId: ModuleId.TALENT, permission: 'any' },
    'UsersList': { moduleId: ModuleId.USERS, permission: 'list' },
    'AddUser': { moduleId: ModuleId.USERS, permission: 'create' },
    'EditUser': { moduleId: ModuleId.USERS, permission: 'edit' },
    'RolesList': { moduleId: ModuleId.ROLES, permission: 'list' },
    'AddRole': { moduleId: ModuleId.ROLES, permission: 'create' },
    'EditRole': { moduleId: ModuleId.ROLES, permission: 'edit' },
    'ClientsList': { moduleId: ModuleId.CLIENTS, permission: 'list' },
    'AddClient': { moduleId: ModuleId.CLIENTS, permission: 'create' },
    'EditClient': { moduleId: ModuleId.CLIENTS, permission: 'edit' },
    'EquipmentList': { moduleId: ModuleId.EQUIPMENT, permission: 'list' },
    'AddEquipment': { moduleId: ModuleId.EQUIPMENT, permission: 'create' },
    'EditEquipment': { moduleId: ModuleId.EQUIPMENT, permission: 'edit' },
    'HistoryLog': { moduleId: ModuleId.HISTORY_LOG, permission: 'list' },
    'Settings': { moduleId: ModuleId.SETTINGS, permission: 'view' }
  }

  return permissionMap[routeName] || null
}

// Helper function to check if user has required permission
function checkRoutePermission(
  permissionsStore: any,
  requirement: { moduleId: ModuleId; permission: string }
): boolean {
  // Superusers have all permissions - never redirect to 403
  if (permissionsStore.isSuperuser) {
    return true
  }

  // For talent routes or 'any' permission, check if user has any talent permission
  if (requirement.moduleId === ModuleId.TALENT || requirement.permission === 'any') {
    return permissionsStore.hasAnyTalentPermission()
  }

  // Check specific permission for other modules
  return permissionsStore.hasPermission(requirement.moduleId, requirement.permission as any)
}

export default router
