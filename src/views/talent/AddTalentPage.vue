
<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { User, CreditCard, FileText, Briefcase, Heart, Settings, History, ChevronLeft, Plus, Laptop, ShieldX, Network } from 'lucide-vue-next'
import TalentProfile from '@/components/tabs/TalentProfile.vue'
import BankingInfo from '@/components/tabs/BankingInfo.vue'
import Documents from '@/components/tabs/Documents.vue'
import WorkHistory from '@/components/tabs/WorkHistory.vue'
import EquipmentAndSoftware from '@/components/tabs/EquipmentAndSoftware.vue'
import Health from '@/components/tabs/Health.vue'
import JobRelatedInfo from '@/components/tabs/BPORelatedInfo.vue'
import HistoryLog from '@/components/tabs/HistoryLog.vue'
import ThirdPartyIntegrations from '@/components/tabs/ThirdPartyIntegrations.vue'
import { getBackgroundColor, getInitials } from '@/utils/image-background'
import { useTalentProfileStore } from '@/store/talent/useTalentProfileStore'
import { useTalentProfile } from '@/composables/talent/useTalentProfile'
import { usePermissions } from '@/composables/permissions/usePermissions'
import { ModuleId } from '@/types/permissions'

const route = useRoute()
const router = useRouter()
const profilePictureInput = ref<HTMLInputElement>()
const tabNavigationRef = ref<HTMLElement>()

// Permission checks
const {
  canViewTalent,
  canEditTalent,
  canCreateTalent,
  isSuperuser,
  hasAnyTalentPermission
} = usePermissions()

// Check if we're in edit mode (has ID param) or add mode (no ID param)
const isEditMode = computed(() => !!route.params.id)

// Permission validation
const hasRequiredPermission = computed(() => {
  if (isSuperuser.value) return true

  if (isEditMode.value) {
    // For edit mode, user needs view or edit permission
    return canViewTalent.value || canEditTalent.value
  } else {
    // For add mode, user needs create permission
    return canCreateTalent.value
  }
})

// Check if user can access this page at all
const canAccessPage = computed(() => {
  return isSuperuser.value || hasAnyTalentPermission()
})

const talentName = ref('New Talent')

const activeTab = ref(route.meta?.tab as string || 'profile')

const {
  getTalentProfile,
  uploadTalentProfilePicture,
  updateTalentProfilePicture,
  isLoading: profileLoading,
  profilePicture,
} = useTalentProfile();

const { form } = useTalentProfileStore()

// Watch for route changes to update active tab
watch(() => route.meta?.tab, (newTab) => {
  if (newTab && isEditMode.value) {
    activeTab.value = newTab as string
  }
}, { immediate: true })

// Watch for tab changes to update route (only in edit mode)
watch(activeTab, (newTab) => {
  if (isEditMode.value) {
    const currentId = route.params.id
    router.push(`/talent/${currentId}/${newTab}`)
  }
})

// Ensure active tab is always 'profile' in creation mode
watch(isEditMode, (editMode) => {
  if (!editMode) {
    activeTab.value = 'profile'
  }
}, { immediate: true })

// All possible tabs with their permission requirements
const allTabs = [
  { id: 'profile', name: 'Talent Profile', icon: User, moduleId: ModuleId.TALENT },
  { id: 'banking', name: 'Banking Info', icon: CreditCard, moduleId: ModuleId.TALENT },
  { id: 'documents', name: 'Documents', icon: FileText, moduleId: ModuleId.TALENT },
  { id: 'work-history', name: 'Work History', icon: Briefcase, moduleId: ModuleId.TALENT },
  { id: 'job-related', name: 'BPO Related Info', icon: Settings, moduleId: ModuleId.TALENT },
  { id: 'equipment', name: 'IT Related Info', icon: Laptop, moduleId: ModuleId.TALENT },
  { id: 'third-party', name: '3rd Party Integrations', icon: Network, moduleId: ModuleId.TALENT },
  { id: 'health', name: 'Health', icon: Heart, moduleId: ModuleId.TALENT },
  { id: 'history-log', name: 'History Log', icon: History, moduleId: ModuleId.TALENT }
]

// Filter tabs based on permissions and creation mode
const tabs = computed(() => {
  // In creation mode (no route param), only show Talent Profile tab
  if (!isEditMode.value) {
    return allTabs.filter(tab => tab.id === 'profile')
  }

  if (isSuperuser.value) {
    return allTabs // Superusers can see all tabs
  }

  // For regular users, show tabs only if they have any talent permission
  if (!hasAnyTalentPermission()) {
    return [] // Hide all tabs if no talent permissions
  }

  // Show all tabs if user has any talent permission
  return allTabs
})



// Handle mouse wheel scrolling on tab navigation
const handleTabWheelScroll = (event: WheelEvent) => {
  if (tabNavigationRef.value) {
    event.preventDefault()
    const scrollAmount = event.deltaY * 0.5 // Adjust scroll sensitivity
    tabNavigationRef.value.scrollLeft += scrollAmount
  }
}

// Check if tabs are scrollable and update class
const updateScrollableState = () => {
  if (tabNavigationRef.value) {
    const isScrollable = tabNavigationRef.value.scrollWidth > tabNavigationRef.value.clientWidth
    if (isScrollable) {
      tabNavigationRef.value.classList.add('scrollable')
    } else {
      tabNavigationRef.value.classList.remove('scrollable')
    }
  }
}

const handleProfilePictureUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    const file = target.files[0]

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB')
      target.value = ''
      return
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file')
      target.value = ''
      return
    }

    // Validate image dimensions (optional - you can adjust these limits)
    const img = new Image()
    img.onload = () => {
      if (img.width > 2000 || img.height > 2000) {
        alert('Image dimensions should not exceed 2000x2000 pixels')
        target.value = ''
        return
      }
    }
    img.src = URL.createObjectURL(file)

    // Create preview URL immediately for better UX
    const reader = new FileReader()
    reader.onload = (e) => {
      profilePicture.value = e.target?.result as string
    }
    reader.readAsDataURL(file)

    // If in edit mode, upload to server
    if (isEditMode.value && route.params.id) {
      try {
        const talentId = Number(route.params.id)
        const hasExistingPicture = form.pic && form.pic.length > 0

        let uploadedPicUrl: string | undefined
        if (hasExistingPicture) {
          uploadedPicUrl = await updateTalentProfilePicture(talentId, file)
        } else {
          uploadedPicUrl = await uploadTalentProfilePicture(talentId, file)
        }

        // Update the profile picture with the server response
        if (uploadedPicUrl) {
          // Handle base64, full URLs, and relative paths
          if (uploadedPicUrl.startsWith('data:image/')) {
            // Base64 image data
            profilePicture.value = uploadedPicUrl
          } else if (uploadedPicUrl.startsWith('http')) {
            // Full URL
            profilePicture.value = uploadedPicUrl
          } else {
            // Relative path
            profilePicture.value = `${import.meta.env.VITE_API_BASE_URL}${uploadedPicUrl}`
          }
        }
      } catch (error) {
        console.error('Failed to upload profile picture:', error)
        // Keep the preview but show error
        alert('Failed to upload profile picture. Please try again.')
      }
    }

    // Reset input
    target.value = ''
  }
}

onMounted(async () => {
  // Superusers never get redirected to 403
  if (isSuperuser.value) {
    // Continue with loading logic for superusers
  } else {
    // Check permissions for regular users
    if (!canAccessPage.value) {
      // User cannot access talent module at all
      router.push('/403')
      return
    }

    if (!hasRequiredPermission.value) {
      // User can access talent module but doesn't have specific permission for this action
      router.push('/403')
      return
    }
  }

  const talentId = route.params.id as string
  if (talentId) {
    await getTalentProfile(Number(route.params.id));
    talentName.value = form.firstName + ' ' + form.lastName
    // Handle profile picture - check if it's base64, full URL, or relative path
    if (form.pic) {
      if (form.pic.startsWith('data:image/')) {
        // Base64 image data
        profilePicture.value = form.pic
      } else if (form.pic.startsWith('http')) {
        // Full URL
        profilePicture.value = form.pic
      } else {
        // Relative path - construct the full URL
        profilePicture.value = `${import.meta.env.VITE_API_BASE_URL}${form.pic}`
      }
    } else {
      profilePicture.value = ''
    }
  }

  // Initialize scrollable state after DOM is ready
  setTimeout(() => {
    updateScrollableState()
    // Add resize listener to update scrollable state on window resize
    window.addEventListener('resize', updateScrollableState)
  }, 100)
})

// Cleanup event listeners
onUnmounted(() => {
  window.removeEventListener('resize', updateScrollableState)
})
</script>

<template>
  <div class="mx-auto w-full max-w-none" style="width: 90%;">
    <!-- Breadcrumb Navigation -->
    <div class="mb-4">
      <button 
        @click="$router.push('/talent')"
        class="flex items-center text-sm font-medium text-gray-600 hover:text-gray-900"
      >
        <ChevronLeft :size="16" class="mr-1" />
        Talent
      </button>
    </div>

    <!-- Page Header -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <div class="flex items-center space-x-3">
          <div class="relative">
            <!-- Profile Picture or Initials -->
            <img 
              v-if="profilePicture"
              :src="profilePicture"
              :alt="talentName"
              class="object-cover w-12 h-12 rounded-full border-2 border-gray-200"
            >
            <!-- Initials for edit mode when no picture -->
            <div 
              v-else-if="isEditMode"
              :class="['flex justify-center items-center w-12 h-12 rounded-full border-2 border-gray-200 font-medium text-sm', getBackgroundColor(talentName)]"
            >
              {{ getInitials(talentName) }}
            </div>
            <!-- Empty state for add mode -->
            <div 
              v-else
              class="flex justify-center items-center w-12 h-12 bg-gray-100 rounded-full border-2 border-gray-300"
            >
              <User :size="24" class="text-gray-400" />
            </div>
            
            <!-- Use label for direct file input trigger -->
            <label
              for="profile-picture-input"
              class="flex absolute -right-1 -bottom-1 justify-center items-center w-6 h-6 text-white bg-orange-500 rounded-full shadow-sm transition-colors cursor-pointer hover:bg-orange-600"
              :class="{ 'opacity-50 cursor-not-allowed pointer-events-none': profileLoading }"
            >
              <div v-if="profileLoading" class="w-3 h-3 rounded-full border border-white animate-spin border-t-transparent"></div>
              <Plus v-else :size="12" />
            </label>
          </div>

          <!-- File input for profile picture -->
          <input
            id="profile-picture-input"
            ref="profilePictureInput"
            type="file"
            @change="handleProfilePictureUpload"
            class="sr-only"
            accept="image/*"
          />
          <div>
            <h1 class="text-xl font-semibold text-gray-900">{{ talentName }}</h1>
          </div>
        </div>
        <div v-if="isEditMode">
          <span class="inline-flex items-center px-3 py-1 text-sm font-medium text-green-800 bg-green-100 rounded-full">
            Active
          </span>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
      <!-- Creation Mode Info Banner -->
      <div v-if="!isEditMode" class="px-6 py-3 bg-blue-50 border-b border-blue-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-blue-700">
              Complete the talent profile first. Additional tabs will be available after creating the talent record.
            </p>
          </div>
        </div>
      </div>

      <!-- Enhanced Tab Navigation -->
      <div
        v-if="tabs.length > 0"
        ref="tabNavigationRef"
        class="overflow-x-auto border-b border-gray-200 tab-navigation"
        @wheel="handleTabWheelScroll"
      >
        <nav class="flex px-6 space-x-1 min-w-max">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'tab-button flex items-center py-4 px-6 border-b-2 font-medium text-sm whitespace-nowrap min-w-fit rounded-t-lg',
              activeTab === tab.id
                ? 'active border-primary-500 text-primary-600 bg-primary-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
            ]"
          >
            <component :is="tab.icon" :size="16" class="flex-shrink-0 mr-3" />
            <span>{{ tab.name }}</span>
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="p-8">
        <!-- Show content if user has permissions or is superuser -->
        <div v-if="isSuperuser || hasRequiredPermission">
          <TalentProfile v-if="activeTab === 'profile'" />
          <BankingInfo v-else-if="activeTab === 'banking'" />
          <Documents v-else-if="activeTab === 'documents'" />
          <EquipmentAndSoftware v-else-if="activeTab === 'equipment'" />
          <WorkHistory v-else-if="activeTab === 'work-history'" />
          <ThirdPartyIntegrations v-else-if="activeTab === 'third-party'" />
          <Health v-else-if="activeTab === 'health'" />
          <JobRelatedInfo v-else-if="activeTab === 'job-related'" />
          <HistoryLog v-else-if="activeTab === 'history-log'" />
        </div>

        <!-- Permission denied message (only for non-superusers) -->
        <div v-else-if="!isSuperuser" class="py-12 text-center">
          <div class="flex justify-center items-center mx-auto mb-4 w-16 h-16 bg-red-100 rounded-full">
            <ShieldX :size="32" class="text-red-600" />
          </div>
          <h3 class="mb-2 text-lg font-medium text-gray-900">Access Denied</h3>
          <p class="mb-4 text-gray-500">
            You don't have permission to {{ isEditMode ? 'view or edit' : 'create' }} talent information.
          </p>
          <button
            @click="$router.push('/talent')"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-md border border-transparent hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            Back to Talent List
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
